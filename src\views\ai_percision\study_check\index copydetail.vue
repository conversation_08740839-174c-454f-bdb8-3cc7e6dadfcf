<template>
    <div class="container" v-loading="writeState.loading">
      <div class="left">
        <div class="test-content">
            <div v-for="(item, index) in allTest" :key="item.quesId" class="test-content-ques" :class="setClass(item, index)" >
                <div class="squre"></div>
                <div class="test-tittle">
                    <div v-html="resetSty(item, index + 1)" />
                </div>
                <div class="test-body" v-html="resetOptions(item)" />
                <div v-if="writeState.current == index">
                    <div v-if="item.ques.cate == 1 || item.ques.cate == 3" class="paper-content-ques">
                        <div v-if="item.ques.cate == 1">
                            <el-checkbox-group v-model="item.ques.userJson" class="checkbox-style-checkbox">
                                <el-checkbox
                                    v-for="(it,ind) in item.ques.options"
                                    :key="ind"
                                    @change="checkChange(ind,index)"
                                    :label="ind"
                                    size="large" border
                                >
                                {{ String.fromCharCode(65 + ind) }}
                                </el-checkbox>
                            </el-checkbox-group>
                        </div>
                        <div v-else>
                            <el-checkbox-group v-model="item.ques.userJson" class="checkbox-style-checkbox">
                                <el-checkbox
                                    v-for="(it,ind) in item.ques.options"
                                    :key="ind"
                                    :label="ind"
                                    size="large" border
                                >
                                {{ String.fromCharCode(65 + ind) }}
                                </el-checkbox>
                            </el-checkbox-group>
                        </div>
                    </div>
                    <div v-else>
                        <div v-show="!writeState.showCorrect">
                            <div class="show-analyse">
                                <el-switch @change="togAnswer(item,item.showAnalyse)"  size="small" v-model="item.showAnalyse" /> <span>显示答案与解析</span>
                            </div>
                            <div v-show="item.showAnalyse" class="analyse">
                                <div class="flex-sty">
                                    <span>【知识点】</span>&nbsp;&nbsp;
                                    <div v-html="item.ques.pointVos[0].name" />
                                </div>
                                <div class="flex-sty">
                                    <span>【答案】</span>&nbsp;&nbsp;
                                    <div v-html="item.ques.displayAnswer" />
                                </div>
                                <div class="flex-sty">
                                    <span>【分析】</span>&nbsp;&nbsp;
                                    <div v-html="item.ques.analyse" />
                                </div>
                                <div class="flex-sty">
                                    <span>【解答】</span>&nbsp;&nbsp;
                                    <div v-html="item.ques.method" />
                                </div>
                                <div class="flex-sty">
                                    <span>【点评】</span>&nbsp;&nbsp;
                                    <div v-html="item.ques.discuss" />
                                </div>
                            </div>
                        </div>
                        <div class="paper-content-ques">
                            <div class="answer-img-box" v-show="!writeState.showCorrect">
                                <el-image
                                    class="answer-img"
                                    v-for="(it, ind) in item.userAnswer"
                                    style="width: 10.8125rem; height: 10.8125rem;border-radius: .25rem;"
                                    :src="it"
                                    :zoom-rate="1.2"
                                    :max-scale="7"
                                    :min-scale="0.2"
                                    :preview-src-list="item.userAnswer"
                                    show-progress
                                    :initial-index="ind"
                                    fit="cover"
                                />
                            </div>
                            <uploadAnswerImg v-show="writeState.showCorrect" :imgList="item.ques.userJson" :index="index" @getImgList="handleImgList" />
                            
                        </div>
                    </div>
                    
                    <div v-if="item.ques.userJson && item.ques.userJson.length > 0" style="display: flex;flex-direction: row-reverse;">
                        <div v-if="item.ques.cate == 1 || item.ques.cate == 3" class="blue-btn" @click="handleSubmit(item, index)">
                            做完了 去批改
                        </div>
                        <div v-else>
                            <div v-show="writeState.showCorrect" class="blue-btn" @click="nonSelectFinish(index)">做完了 去批改</div>
                            <div v-show="!writeState.showCorrect">
                                <div class="answers">
                                    <div class="answer-box" @click="correcthandle(index, 1)" :class="item.userMarks == 1?'green-box':''">
                                        <div></div>正确
                                    </div>
                                    <div class="answer-box" @click="correcthandle(index, 2)" :class="item.userMarks == 2?'yellow-box':''">
                                        <div></div>半对
                                    </div>
                                    <div class="answer-box" @click="correcthandle(index, 0)" :class="item.userMarks == 0?'red-box':''">
                                        <div></div>错误
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-else>
                    <div v-if="item.userAnswer && item.userAnswer.length > 0">
                        <div v-if="item.ques.cate == 1 || item.ques.cate == 3" class="answers">
                            <div v-for="(it,ind) in item.ques.options" :class="item.userAnswer.includes(ind.toString())?(item.ques.answers?.includes(ind.toString())?'selected':'wrong-selected'):''" class="answer-box">
                                <div></div>{{ String.fromCharCode(65 + ind) }}
                            </div>
                        </div>
                        <div v-else>
                            <div class="answer-img-box">
                                <el-image
                                    class="answer-img"
                                    v-for="(it, ind) in item.userAnswer"
                                    style="width: 10.8125rem; height: 10.8125rem;border-radius: .25rem;"
                                    :src="it"
                                    :zoom-rate="1.2"
                                    :max-scale="7"
                                    :min-scale="0.2"
                                    :preview-src-list="item.userAnswer"
                                    show-progress
                                    :initial-index="ind"
                                    fit="cover"
                                />
                            </div>
                            <div class="answers">
                                <div class="answer-box" :class="item.userMark == 1?'green-box':''">
                                    <div></div>正确
                                </div>
                                <div class="answer-box" :class="item.userMark == 2?'yellow-box':''">
                                    <div></div>半对
                                </div>
                                <div class="answer-box" :class="item.userMark == 0?'red-box':''">
                                    <div></div>错误
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
      </div>
      <div class="right">
        <div class="time-box">
            <div class="time-text">用时:</div>
            <div class="time-number"> {{ timeState.hours < 10 ? "0" + timeState.hours : timeState.hours }} </div> :
            <div class="time-number"> {{ timeState.minutes < 10 ? "0" + timeState.minutes : timeState.minutes }} </div> :
            <div class="time-number"> {{ timeState.seconds < 10 ? "0" + timeState.seconds : timeState.seconds }} </div>
        </div>
        <div class="test-number-box">
            <div class="test-number-item" v-for="(item, index) in allTest" 
                 :class="setClass1(item, index)" 
                 @click="switchQuestion(index)"> {{ index + 1 }} </div>
            <div class="icon-btn size285" :class="writeState.disabled?'disabled':''" @click="submit" v-loading="writeState.btnloading">
                <img src="@/assets/img/percision/submit.png" alt="">
                提交批改
            </div>
        </div>
      </div>
      <div class="five-step-box" v-if="writeState.showStep">
        <fiveStep :sourceId="queryData.sourceId" :type="queryData.type==='synchronous'?1:2" :update="false" @sendStep="sendStep"></fiveStep>
      </div>
    </div>
    <coinAlert :show="writeState.jfShow" :num="writeState.jfNum" :isAjax="false" @close="jfHide">
    </coinAlert>
</template>
  
<script lang="ts" setup>
import { watch, onMounted, reactive, ref, onUnmounted } from 'vue'
import uploadAnswerImg from '@/views/components/uploadAnswerImg/index.vue'
import coinAlert from "@/views/components/coinAlert/index.vue"
import { dataDecrypt, dataEncrypt, mergeObject } from "@/utils/secret"
import { useRouter, useRoute } from 'vue-router'
import fiveStep from "@/views/components/fiveStep/index.vue"
import { quesGetApi} from "@/api/video"
import { Action, ElMessage, ElMessageBox } from 'element-plus'
import { createTrainToAtlasApi, getDetailsApi, saveToAtlasApi, checkTrainQuesApi } from '@/api/training'
const route = useRoute()
const router = useRouter()
const timeState = reactive({
    hours: 0,
    minutes: 0,
    seconds: 0
})
const writeState = reactive({
    current: 0 as number | null,
    step: 1,
    btnloading: false,
    showCorrect: true,
    loading: false,
    showStep: false,
    disabled: true,
    trainingId: "",
    //积分
    jfShow: false,
    jfHide: true,
    jfNum: '0',
    jfSource: '0',
    itemTimer: 0, // 单题计时器
    lastTimestamp: 0 // 上次计时时间戳
})
interface Ques {
    cate: number;
    cateName: string;
    content: string;
    displayAnswer: string;
    analyse: string;
    method: string;
    discuss: string;
    options: any[];
    pointVos: any[];
    userJson: any[];
    answers: any[];
}

class AData {
    quesId: string = "";
    cate: number = 0;
    cateName: string = "";
    trainTime: string = "";
    userAnswer: string[] = [];
    userMark: number | null = null;
    userMarks: number | null = null;
    showAnalyse: boolean = false;
    content: string = "";
    ques: Ques = { // 添加 ques 属性
        cate: 0,
        cateName: "",
        content: "",
        analyse: "",
        discuss: "",
        method: "",
        displayAnswer: "",
        options: [],
        pointVos: [],
        userJson: [],
        answers: []
    };
}
let detailData
let timer :  NodeJS.Timeout | null = null
const queryData = route.query.data ? dataDecrypt(route.query.data) : {}
const dataType =Number(queryData.dataType) || 1

console.log("queryData--",queryData)
const allTest = ref([] as AData[])
watch(() => timeState.seconds, () => {
    if(timeState.seconds == 60) {
        timeState.minutes ++
        timeState.seconds = 0
    }
    if(timeState.minutes == 60) {
        timeState.hours ++
        timeState.minutes = 0
    }

})

onUnmounted(() => {
    if (timer !== null) { // 添加类型安全检查
        clearInterval(timer)
        timer = null // 确保timer被清空
    }
    // 重置计时器状态
    writeState.itemTimer = 0
    
    // 清除自定义返回方法
    if (window.customGoBack) {
        delete window.customGoBack
    }
})
// 隐藏积分
const jfHide = () => {
    writeState.jfShow = false
}
// 获取学习步骤
const sendStep = ( data: number) => {
    writeState.step = data
}

  //显示答案
const togAnswer = async (item:any,isShow:any) => { 
    if(isShow){
        // 如果已经有完整的题目信息，直接显示，无需重复请求
        if (item.ques.analyse && item.ques.method && item.ques.discuss) {
            return
        }   
        try {
            // 添加加载状态，防止重复点击
            if (item.loading) return
            item.loading = true
            
            const response = await quesGetApi({id: item.ques.quesId}) as any
            
            if (response.code === 200 && response.data) {
                // 使用 Object.assign 来安全地合并数据，保留原有属性
                Object.assign(item.ques, response.data)
                
                // 确保必要的属性存在
                if (!item.ques.pointVos) {
                    item.ques.pointVos = []
                }
                if (!item.ques.options) {
                    item.ques.options = []
                }
                if (!item.ques.answers) {
                    item.ques.answers = []
                }
                console.log('题目详细信息已更新:', item.ques)
            } else {
                console.error('获取题目详细信息失败:', response)
                // 如果获取失败，关闭显示开关
                item.showAnalyse = false
                // 可以添加用户提示
            }
        } catch (error) {
            console.error('获取题目详细信息时发生错误:', error)
            // 发生错误时关闭显示开关
            item.showAnalyse = false
            // 可以添加用户提示
        } finally {
            // 清除加载状态
            item.loading = false
        }
    }

}
const createTrain = () => {
    const formdata = new FormData()
    formdata.append("sourceId", queryData.sourceId)
    if(queryData.type !== 'synchronous'){
        formdata.append("noteSource", '2',)
    }else{
        formdata.append("noteSource", '1')
    }  

    for(let i of queryData.pointId){
      formdata.append("pointIds[]", i)
    }
    formdata.append("step", queryData.step)
    formdata.append("isPromote", queryData.type === 'improvement' ? '1' : '0')
    createTrainToAtlasApi(formdata).then((res: any) => {
        if (res.data) {
            writeState.trainingId = res.data
            writeState.showStep = true
            
            // 重置计时器状态
            writeState.itemTimer = 0
            timeState.seconds = 0
            timeState.minutes = 0
            timeState.hours = 0
            
            // 确保计时器正常运行
            if (timer === null) {
                timer = setInterval(() => {
                    timeState.seconds++
                    writeState.itemTimer++ // 更新单题计时
                }, 1000)
            }
            
            // 获取详情
            getDetails()
        }
    }).catch((error) => {
    })
}
const getDetails = () => {
    writeState.loading = true
    writeState.showCorrect = true
    getDetailsApi({trainingId: writeState.trainingId}).then((res1: any) => {
        if (res1.code == 200) {
            detailData = res1.data
            timeState.seconds = Number(res1.data.trainTime) / 1000
            res1.data.items.forEach((item) => {
                item.showAnalyse = false
                item.ques.userJson = []
            })
            allTest.value = res1.data.items
            
            // 检查是否有未完成的题目
            let hasUnansweredQuestions = false;
            
            for (let i = 0; i < res1.data.items.length; i++) {
                if(res1.data.items[i].userMark == null) {
                    writeState.current = i
                    writeState.itemTimer = 0 // 重置单题计时器
                    hasUnansweredQuestions = true;
                    break
                } else {
                    writeState.current = null
                }
            }
            
            // 如果所有题目都已完成，停止计时器
            if (!hasUnansweredQuestions && timer !== null) {
                clearInterval(timer);
                timer = null;
                console.log('所有题目已完成，停止计时器');
            }
            
            const clickable = allTest.value.find(item => item.userMark == null)
            writeState.disabled = clickable?true:false
        }
        writeState.loading = false

    }).catch((error) => {
        writeState.loading = false
    })
}
const setClass = (item: any, index: number) => {
    let classState = ""
    if (item.userMark != null) {
        if (item.userMark == 0) {
            classState = "red-border"
        } else if (item.userMark == 1) {
            classState = "green-border"
        } else if (item.userMark == 2) {
            classState = "yellow-border"
        }

    } else if (writeState.current == index) {
        classState = "black-text"
    }
    return classState
}
const setClass1 = (item: any, index: number) => {
    let classState = ""
    if (item.userMark != null) {
        if (item.userMark == 0) {
            classState = "red"
        } else if (item.userMark == 1) {
            classState = "green"
        } else if (item.userMark == 2) {
            classState = "yellow"
        }

    } else if (writeState.current == index) {
        classState = "blue"
    }
    return classState
}

// 添加点击题号切换题目的方法
const switchQuestion = (index: number) => {
    // 只有未完成的题目才能切换
    const item = allTest.value[index]
    if (item && item.userMark === null) {
        // 重置单题计时器
        writeState.itemTimer = 0
        writeState.current = index
    }
}

// 监听当前题目变化
watch(() => writeState.current, (newVal, oldVal) => {
    if (newVal !== oldVal && newVal !== null) {
        // 切换题目时重置单题计时器
        writeState.itemTimer = 0
    }
})

const correcthandle = (index: number, userMark: number) => {
    // 验证 index 是否在有效范围内
    if (index < 0 || index >= allTest.value.length) {
        console.error(`Invalid index: ${index}`);
        return;
    }

    // 确保 allTest.value[index] 存在且 userJson 已初始化
    const currentItem = allTest.value[index];
    if (!currentItem) {
        console.error(`Item at index ${index} is undefined`);
        return;
    }

    // 更新 userJson
    currentItem.userMarks = userMark;
    const data = JSON.parse(JSON.stringify(currentItem))
    data.userMark = userMark;
    const arr = [] as string[]
    data.ques.userJson.map((item: any) => {
        arr.push(item.url)
    })
    data.ques.userJson = arr
    // 在这里不重置计时器，让handleSubmit统一处理
    handleSubmit(data, index)
}
// 非选择题自主批改
const nonSelectFinish = (index: number) => {
    // 验证 index 是否在有效范围内
    if (index < 0 || index >= allTest.value.length) {
        console.error(`Invalid index: ${index}`);
        return;
    }

    // 确保 allTest.value[index] 存在且 userJson 已初始化
    const currentItem = allTest.value[index];
    if (!currentItem) {
        console.error(`Item at index ${index} is undefined`);
        return;
    }

    // 更新 userJson
    const arr = [] as string[]
    currentItem.ques.userJson.map((item: any) => {
        arr.push(item.url)
    })
    currentItem.userAnswer = arr
    writeState.showCorrect = false
    // 注意：这里不重置计时器，因为用户还需要进行自主批改
    // 计时器会在correcthandle调用handleSubmit时重置
}
const checkChange = (val: any, index: number) => {
    // 验证 index 是否在有效范围内
    if (index < 0 || index >= allTest.value.length) {
        console.error(`Invalid index: ${index}`);
        return;
    }

    // 确保 allTest.value[index] 存在且 userJson 已初始化
    const currentItem = allTest.value[index]?.ques;
    if (!currentItem) {
        console.error(`Item at index ${index} is undefined`);
        return;
    }

    // 更新 userJson
    currentItem.userJson = [val];
}
const handleImgList = (index: number, imgList: any) => {
        // 验证 index 是否在有效范围内
    if (index < 0 || index >= allTest.value.length) {
        console.error(`Invalid index: ${index}`);
        return;
    }

    // 确保 allTest.value[index] 存在且 userJson 已初始化
    const currentItem = allTest.value[index]?.ques;
    if (!currentItem) {
        console.error(`Item at index ${index} is undefined`);
        return;
    }

    // 更新 userJson
    currentItem.userJson = imgList;
    // allTest.value[index].userJson = imgList
}
//过滤修改题干标签内容
const resetSty = function (testItem: any, sort?: number) {
  const tittle = "（" + testItem.ques.cateName + "）" + sort + "." + '&nbsp;&nbsp;' + filterContent(testItem.ques.content)
  return tittle
}
//过滤掉填空题等div标签的contenteditable属性
const filterContent = (contect: string) => {
  let highlightedResult = ""
  highlightedResult = contect.replaceAll('contenteditable="true"', " ")
  highlightedResult = highlightedResult.replaceAll("contenteditable='true'", " ")
  return highlightedResult
}
//过滤修改选项内容
const resetOptions = function (testItem: any) {
    let optionHtml = ""
    if (!testItem.ques.options) return
    testItem.ques.options.map((item: any, index: number) => {
        optionHtml += `<div class="answer-item"> ${String.fromCharCode(65 + index)}. <div style="display:inline-table;max-width:39.8125rem">${item}</div></div>`
    })
    return optionHtml
}
const handleSubmit = (data: any, index: number) => {
    // 计算单题训练时长 - 修改为使用独立计时器
    let userMark = data.userMark
    const params = {
        trainingItemId: data.trainingItemId,
        trainTime: timeState.seconds * 1000,
        trainItemTime: writeState.itemTimer * 1000, // 使用独立的单题计时器
        userJson: data.ques.userJson,
        userMark: userMark,
        noteSource: data.ques.noteSource,
        cate: data.ques.cate
    }
    if(data.ques.cate == 1 || data.ques.cate == 3) {
        delete params.userMark
    }
    writeState.loading = true
    checkTrainQuesApi(params)
    .then((res: any) => {
        ElMessage({
            message: '批改成功',
            type: 'success'
        })
        writeState.itemTimer = 0 // 重置单题计时器
        
        // 检查是否为最后一道题
        const isLastQuestion = !allTest.value.some((item, i) => {
            // 跳过当前题目和已完成的题目
            return i !== index && item.userMark === null;
        });
        
        // 如果是最后一道题，停止计时器
        if (isLastQuestion && timer !== null) {
            clearInterval(timer);
            timer = null;
            console.log('最后一道题已完成，停止计时器');
        }
        
        getDetails()
        writeState.loading = false
    })
    .catch(() => {
        writeState.loading = false
    })
}
const submit = () => {
    if (!writeState.disabled) {
        writeState.btnloading = true
        const items = detailData.items.map((item: any, index: number) => {
            return {
                cate: item.ques.cate,
                trainingItemId: item.trainingItemId,
                userJson: item.userAnswer,
                userMark: item.userMark,
            }
        })
        
        // 确保使用最新的计时值
        const finalTrainTime = timer !== null ? timeState.seconds * 1000 : detailData.trainTime;
        
        const params = {
            trainingId: detailData.trainingId,
            trainTime: finalTrainTime, // 使用最新的时间或保留原始时间
            items,
            reviseCount: detailData.reviseCount || null,
            status: detailData.status,
            score: detailData.score,
            correctRate: detailData.correctRate,
        }
        saveToAtlasApi(params)
        .then((res: any) => {
            if (timer !== null) { // 添加类型安全检查
                clearInterval(timer)
                timer = null // 确保timer被清空
            }
            // 重置计时器
            writeState.itemTimer = 0
            
            ElMessage({
                message: '提交成功',
                type: 'success'
            })
            getTringDetail()
        })
        .catch(() => {
            writeState.btnloading = false
        })
    }
}
// 获取训练报告数据
const getTringDetail = () => {
    // 生成诊断报告弹窗暂未加上

    getDetailsApi({trainingId: writeState.trainingId}).then((res: any) => {
        if (res.code == 200) {
            const data = res.data
            localStorage.setItem('diagnosticReport', JSON.stringify(data))
            writeState.btnloading = false
            writeState.jfNum = res.data.integral
            writeState.jfShow = true
            writeState.jfHide = false
            setTimeout(()=>{
                router.push({
                    path: '/ai_percision/knowledge_graph_detail/training_report',
                    query: { 
                        data: dataEncrypt({
                            sourceId: queryData.sourceId,
                            showStep: dataType,
                            type:queryData.type,
                            contentType:queryData.contentType
                        })
                    }
                })
            },3000)
        }
    }).catch((error) => {
    })
}

// 自定义返回方法
const customGoBack = () => {
     router.go(-1)
}
// onMounted(() => {
//     createTrain()
//     timer = setInterval(() => {
//         timeState.seconds ++
//     }, 1000)
// })
onMounted(() => {
    console.log(queryData,"weekness_checkweekness_checkweekness_check")
    // 注册自定义返回方法
    window.customGoBack = customGoBack
    
    // 初始化时间戳
    writeState.lastTimestamp = Date.now()
    
    // 创建训练，计时器会在createTrain中启动
    createTrain()
    
    // 移除这里的计时器启动，避免重复启动
    // timer = setInterval(() => {
    //     timeState.seconds++
    //     writeState.itemTimer++ // 更新单题计时
    // }, 1000)
})
onUnmounted(() => {
 if (timer !== null) { // 添加类型安全检查
clearInterval(timer)
}
// 清除自定义返回方法
 if (window.customGoBack) {
delete window.customGoBack
}
})
const handleAnalysis = () => {
    router.push({ 
        path: '/ai_percision/knowledge_graph_detail/paper_analysis'
    })
}
</script>
<style lang="scss" scoped>
.container {
    display: flex;
    position: relative;
    .five-step-box {
        position: absolute;
        right: -8.75rem;
        top: 11.25rem;
    }
    .left {
        width: 60.3125rem;
        .test-content {
            height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height));
            box-sizing: border-box;
            overflow-y: auto;
            .test-content-ques {
                background: #ffffff;
                width: 100%;
                box-sizing: border-box;
                padding: 1.25rem 0;
                margin-bottom: .625rem;
                position: relative;
                color: #999999;
                .squre {
                    width: .875rem;
                    height: 1rem;
                    border-radius: 0 .375rem .375rem 0;
                    background: #5a85ec;
                    position: absolute;
                    top: 1.625rem;
                    left: 0;
                }
                .test-tittle,.test-body {
                    padding: 0 1.875rem;
                }
            }
        }
    }
    .right {
        padding: 1.125rem 0;
        width: 20.3125rem;
        margin-left: .625rem;
        background: #ffffff;
        box-sizing: border-box;
        height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height));
        .time-box {
            display: flex;
            align-items: center;
            color: #2a2b2a;
            font-size: 1.875rem;
            font-weight: 700;
            padding-left: .625rem;
            padding-bottom: 1.25rem;
            border-bottom: .0625rem dashed #eaeaea;
            .time-text {
                font-size: 1rem;
                font-weight: 700;
            }
            .time-number {
                width: 3.75rem;
                height: 3.75rem;
                line-height: 3.75rem;
                text-align: center;
                border-radius: .25rem;
                border: .0625rem solid #eaeaea;
                background: #f5f5f5;
                margin: 0 .625rem;
            }
        }
        .test-number-box {
            height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height) - 8.125rem);
            box-sizing: border-box;
            padding: 1.25rem .625rem 1.25rem 0;
            .test-number-item {
                display: inline-block;
                margin-left: .625rem;
                margin-bottom: .625rem;
                border-radius: .25rem;
                width: 2.5rem;
                height: 2.5rem;
                line-height: 2.5rem;
                text-align: center;
                color: #2a2b2a;
                font-size: 1rem;
                font-weight: 400;
                border: .0625rem solid #eaeaea;
                background: #f5f5f5;
                box-sizing: border-box;
            }
            .disabled {
                background: #bebebe;
                cursor: not-allowed;
            }
            .blue {
                background: #5a85ec;
                color: #ffffff;
            }
            .red {
                background: #dd2a2a;
                color: #ffffff;
            }
            .green {
                background: #00c9a3;
                color: #ffffff;
            }
            .yellow {
                background: #f1be21;
                color: #ffffff;
            }
            .size285 {
                width: 17.8125rem;
                height: 2.75rem;
                font-size: 1rem;
                font-weight: 700;
                margin-left: .625rem;
                margin-top: 1.25rem;
                img {
                    width: 1rem;
                    height: 1rem;
                }
            }
        }
    }
}
.icon-btn {
    border-radius: .25rem;
    background: #00c9a3;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    img {
        margin-right: .3125rem;
    }
}
.answer-img-box {
    padding-left: 1.875rem;
    padding-top: 1.25rem;
    .answer-img {
        width: 10.8125rem;
        height: 10.8125rem;
        border-radius: .25rem;
        margin-right: .625rem;
    }
}
:deep(.el-checkbox-group) {
    .el-checkbox {
        width: 6.25rem;
        height: 3.125rem;
        margin-right: 1.25rem;
        display: inline-flex;
        justify-content: center;
    }
    .is-checked {
        .el-checkbox__inner {
            background: #5a85ec;
            border: .0625rem solid #5a85ec;
        }
        .el-checkbox__label {
            color: #5a85ec;
        }
    }
    .el-checkbox__inner {
        &:hover {
            border: .0625rem solid #5a85ec;
        }
    }
}
.show-analyse {
    width: 100%;
    background: #fef8e9;
    padding-left: 1.875rem;
    height: 2.1875rem;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    color: #666666;
    font-size: .75rem;
    font-weight: 400;
    margin-top: 1.25rem;
    span {
        margin-left: .375rem;
    }
}
.analyse {
    padding: .625rem 1.875rem;
    letter-spacing: .125rem;
    background: #fef8e9;
    div {
      margin-bottom: .625rem;
    }
}
.flex-sty {
  display: flex;
  font-size: .875rem;
  align-items: baseline;
  div {
    max-width: 52.375rem;
    line-height: 1.0625rem;
  }
  span {
    text-wrap: nowrap;
    font-weight: 700;
    letter-spacing: normal;
  }
}
.paper-content-ques {
    margin-top: 1.25rem;
    border-top: .0625rem dashed #EAEAEA;
    padding: 1.25rem 1.875rem;
}
.blue-btn {
    width: 7.625rem;
    height: 2.375rem;
    line-height: 2.375rem;
    text-align: center;
    border-radius: 1.1875rem;
    background: #00c9a3;
    color: #ffffff;
    font-size: 1rem;
    cursor: pointer;
    margin-right: .625rem;
    font-weight: 400;
}
.black-text {
    color: black!important;
}
.red-border {
    border: .0625rem solid #dd2a2a;
    color: black!important;
    .squre {
        background: #dd2a2a!important;
    }
    .wrong-selected {
        border: .0625rem solid #dd2a2a!important;
        background: #fce9e9!important;
        div {
            background-image: url(@/assets/img/percision/wrong-check.png);
            background-size: 100% 100%;
            background-repeat: no-repeat;
        }
    }
}
.green-border {
    color: black!important;
    border: .0625rem solid #00C9A3;
    .squre {
        background: #00C9A3!important;
    }
    .selected {
        border: .0625rem solid #00c9a3!important;
        background: #e5f9f6!important;
        div {
            background-image: url(@/assets/img/percision/right-check.png);
            background-size: 100% 100%;
            background-repeat: no-repeat;
        }
    }
}
.yellow-border {
    color: black!important;
    border: .0625rem solid #f1be21;
    .squre {
        background: #f1be21!important;
    }
    .selected {
        border: .0625rem solid #00c9a3!important;
        background: #e5f9f6!important;
        div {
            background-image: url(@/assets/img/percision/right-check.png);
            background-size: 100% 100%;
            background-repeat: no-repeat;
        }
    }
    .wrong-selected {
        border: .0625rem solid #dd2a2a!important;
        background: #fce9e9!important;
        div {
            background-image: url(@/assets/img/percision/wrong-check.png);
            background-size: 100% 100%;
            background-repeat: no-repeat;
        }
    }
}
.green-box {
    border: .0625rem solid #00c9a3!important;
    background: #e5f9f6!important;
    div {
        background-image: url(@/assets/img/percision/right-check.png);
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }
}
.red-box {
    border: .0625rem solid #dd2a2a!important;
    background: #fce9e9!important;
    div {
        background-image: url(@/assets/img/percision/wrong-check.png);
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }
}
.yellow-box {
    border: .0625rem solid #f1be21!important;
    background: #fef8e8!important;
    div {
        background-image: url(@/assets/img/percision/harf-right.png);
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }
}
.answers {
    display: flex;
    margin-top: 1.25rem;
    .answer-box {
        width: 6.25rem;
        height: 3.125rem;
        border-radius: .25rem;
        cursor: pointer;
        border: .0625rem solid #dddddd;
        background: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1.25rem;
        &:first-child {
            margin-left: 1.875rem;
        }
        div {
            border: .0625rem solid #999999;
            width: 1rem;
            height: 1rem;
            border-radius: .125rem;
            margin-right: .625rem;
        }
    }
}
</style>
<style lang="scss">
.answer-item:not(:last-child) {
    margin-bottom: 1.875rem;
}
</style>