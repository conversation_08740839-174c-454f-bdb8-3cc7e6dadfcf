import { createRouter, createWebHistory, type RouteRecordRaw } from 'vue-router'
import Home from '../views/home.vue'
import Layout from '../layout/index.vue'
import Layout2 from '../layout/index2.vue'
import { useUserStoreHook } from '../store/modules/user'
import { Action, ElMessageBox } from 'element-plus'
import TaskListPage from '@/views/school_inspector/TaskListPage.vue'
import HistoryTaskListPage from '@/views/school_inspector/HistoryTaskListPage.vue'


// import { computed } from 'vue'
// const learnUsers = JSON.parse(localStorage.learnUsers || "[]") || []
// const gradeName =  learnUsers[0]?.gradeName
// const knowledgeTitle = computed(() => {
//   const highSchoolGrades = ['高一', '高二', '高三'];
//   if (highSchoolGrades.includes(gradeName)) {
//     return '知识点视频'
//   }
//   return '名师知识点'
// })

const routes: Array<any> = [
  {
    path: '/',
    name: 'Home',
    redirect: "/dashboard",
    component: Home,
    meta: { requiresAuth: true },
  },
  {
    path: "/dashboard",
    name: 'Dashboard',
    component: () => import("@/views/dashboard/index.vue"),
    meta: {
      hidden: true
    }
  },
  {
    path: "/login",
    name: 'Login',
    component: () => import("@/views/login/login.vue"),
    meta: {
      hidden: true
    }
  },
    {
    path: "/mobile_test_report",
    name: 'MobileReport',
    component: () => import("@/views/mobile_test_report/index.vue"),
    meta: {
      hidden: true
    }
  },
    {
    path: "/loginLess",
    name: 'LoginLess',
    component: () => import("@/views/loginLess/index.vue"),
    meta: {
      hidden: true
    }
  },
  {
    path: "/forget",
    name: 'Forget',
    component: () => import("@/views/login/forget.vue"),
    meta: {
      hidden: true
    }
  },
  {
    path: "/user_agent",
    component: () => import("@/views/login/userAgent.vue"),
    meta: {
      hidden: true
    }
  },
  {
    path: "/user_agent2",
    component: () => import("@/views/login/userAgent2.vue"),
    meta: {
      hidden: true
    }
  },

  // 督学
  {
    path: '/school_inspector',
    name: 'schoolInspector',
    component: Layout,
    redirect: "/school_inspector/school_task",
    meta: {
      title: "督学",
      showTab: true,
      icon: 'superintendent',
    },
    children: [
      {
        path: 'school_task',
        name: 'school_task',
        component: () => import('@/views/school_inspector/school_task/index.vue'),
        meta: {
          title: "督学课程",
          showTab: true,
          keepAlive: false,
          isFull: true
        },
      },
      {
        path: 'tasks',
        name: 'InspectorTasks',
        // component: TaskListPage,
        component: () => import('@/views/school_inspector/TaskListPage.vue'),
        meta: {
          title: "督学任务",
          showTab: false,
          keepAlive: false,
        },
          // backPath: '/academic_report/academic_report_page',
          // showTab: false,
      },
      {
        path: 'history_tasks',
        name: 'InspectorHistoryTasks',
        // component: HistoryTaskListPage,
        component: () => import('@/views/school_inspector/HistoryTaskListPage.vue'),
        meta: {
          title: "历史任务",
          showTab: false,
          keepAlive: false,
        },
      },
      // {
      //   path: 'analysis',
      //   name: 'Analysis',
      //   component: () => import('@/views/school_inspector/analysis.vue'),
      //   meta: {
      //     title: "分析",
      //     showTab: true,
      //     requiresNestedView: true
      //   },
      // },
    ]
  },


  {
    path: '/ai_percision',
    name: 'AiPercision',
    component: Layout,
    redirect: "/ai_percision/knowledge_graph",
    meta: {
      title: "AI精准学",
      icon: 'ai-percision',
    },
    children: [
      {
        path: 'knowledge_graph',
        name: 'KnowledgeGraph',
        component: () => import('@/views/ai_percision/knowledge_graph/index.vue'),
        meta: {
          title: "知识图谱",
          showTab: true,
        },
        children: [
          {
            path: 'paper_detail',
            name: 'KnowledgeGraphTestDetail2',
            component: () => import('@/views/true_paper/true_paper_detail/index.vue'),
            meta: {
              title: "试卷详情",
              showTab: false,
              requiresNestedView: true
            },
          },
        ],
      },
      {
        path: 'minesweeper',
        name: 'Minesweeper',
        component: () => import('@/views/ai_percision/minesweeper/index.vue'),
        meta: {
          title: "知识点扫雷",
          showTab: false
        },
        children: [
          {
            path: 'paper_write_switchM',
            name: 'paperWriteWenM',
            component: () => import('@/views/ai_percision/paper_write_switch/index.vue'),
            meta: {
              title: "训练作答",
              showTab: false,
              requiresNestedView: true
            },
          },
          {
            path: 'training_report_wenM',
            name: 'trainingReportWenM',
            component: () => import('@/views/ai_percision/training_report_wen/index.vue'),
            meta: {
              title: "训练报告",
              showTab: false,
              backPath: '/ai_percision/minesweeper',
              requiresNestedView: true
            },
          },
        ]
      },
      {
        path: 'to_practice',
        name: 'ToPractice',
        component: () => import('@/views/ai_percision/to_practice/index.vue'),
        meta: {
          title: "去练习",
          showTab: false
        },
        children: [
          {
            path: 'paper_write_switchP',
            name: 'paperWriteWenP',
            component: () => import('@/views/ai_percision/paper_write_switch/index.vue'),
            meta: {
              title: "训练作答",
              showTab: false,
              backPath: '/ai_percision/to_practice',
              requiresNestedView: true
            },
          },
          {
            path: 'training_report_wenP',
            name: 'trainingReportWenP',
            component: () => import('@/views/ai_percision/training_report_wen/index.vue'),
            meta: {
              title: "训练报告",
              showTab: false,
              backPath: '/ai_percision/to_practice',
              requiresNestedView: true
            },
          },
          {
            path: 'practice_history',
            name: 'PracticeHistory',
            component: () => import('@/views/ai_percision/history/index.vue'),
            meta: {
              title: "历史关卡",
              showTab: false,
              backPath: '/ai_percision/to_practice',
              requiresNestedView: true
            },
          },
        ]
      },
      {
        path: 'go_evaluate',
        name: 'ToEvaluate',
        component: () => import('@/views/ai_percision/go_evaluate/index.vue'),
        meta: {
          title: "去测评",
          showTab: false
        },
        children: [
          {
            path: 'true_paper_detailE',
            name: 'TruePaperDetailE',
            component: () => import('@/views/true_paper/true_paper_detail/index.vue'),
            meta: {
              title: "试卷详情",
              showTab: false,
              requiresNestedView: true
            },
          },
          {
            path: 'true_paper_writeE',
            name: 'TruePaperWriteE',
            component: () => import('@/views/ai_percision/paper_write/index.vue'),
            meta: {
              title: "试卷作答",
              showTab: false,
              requiresNestedView: true
            },
          },
          {
            path: 'training_report_wenE',
            name: 'trainingReportWenE',
            component: () => import('@/views/ai_percision/training_report_wen/index.vue'),
            meta: {
              title: "训练详情",
              showTab: false,
              backPath: '/ai_percision/go_evaluate',
              requiresNestedView: true
            },
          },
          {
            path: 'paper_analysisE',
            name: 'paperAnalysisE',
            component: () => import('@/views/ai_percision/paper_analysis/index.vue'),
            meta: {
              title: "试卷分析",
              showTab: false,
              requiresNestedView: true,
              backPath: '/ai_percision/go_evaluate',
            },
          },
          {
            path: 'my_paperE',
            name: 'MyPaperE',
            component: () => import('@/views/ai_percision/my_paper/index.vue'),
            meta: {
              title: "我的试卷",
              showTab: false,
              requiresNestedView: true,
            },
          },
        ]
      },
      {
        path: 'knowledge_graph_detail',
        name: 'KnowledgeGraphDetail',
        component: () => import('@/views/ai_percision/knowledge_graph_detail/index.vue'),
        meta: {
          title: "知识图谱详情",
          showTab: false,
        },
        children: [
          {
            path: 'paper_detail',
            name: 'KnowledgeGraphTestDetail',
            // component: () => import('@/views/ai_percision/paper_detail/index.vue'),
            component: () => import('@/views/true_paper/true_paper_detail/index.vue'),
            meta: {
              title: "试卷详情",
              showTab: false,
              requiresNestedView: true
            },
          },
          {
            path: 'my_paper',
            name: 'MyPaper',
            component: () => import('@/views/ai_percision/my_paper/index.vue'),
            meta: {
              title: "我的试卷",
              showTab: false,
              requiresNestedView: true
            },
          },
          {
            path: 'paper_write',
            name: 'paperWrite',
            component: () => import('@/views/ai_percision/paper_write/index.vue'),
            meta: {
              title: "试卷作答",
              showTab: false,
              requiresNestedView: true
            },
          },
          {
            path: 'weekness_check',
            name: 'weeknessCheck',
            component: () => import('@/views/ai_percision/study_check/index.vue'),
            meta: {
              title: "弱项检测",
              showTab: false,
              requiresNestedView: true
            },
          },
          {
            path: 'regarding_learning',
            name: 'regardingLearning',
            component: () => import('@/views/ai_percision/regarding_learning/index.vue'),
            meta: {
              title: "针对学习",
              showTab: false,
              requiresNestedView: true
            },
          },
          {
            path: 'study_check',
            name: 'studyCheck',
            component: () => import('@/views/ai_percision/study_check/index.vue'),
            meta: {
              title: "学后检测",
              showTab: false,
              requiresNestedView: true
            },
          },
          {
            path: 'periodical_check',
            name: 'periodicalCheck',
            component: () => import('@/views/ai_percision/study_check/index.vue'),
            meta: {
              title: "阶段测试",
              showTab: false,
              requiresNestedView: true
            },
          },
          {
            path: 'worning_learning',
            name: 'worningLearning',
            component: () => import('@/views/ai_percision/worning_learning/index.vue'),
            meta: {
              title: "错题消化",
              showTab: false,
              requiresNestedView: true
            },
          },
          {
            path: 'paper_write_switchC',
            name: 'worningLearningC',
            component: () => import('@/views/ai_percision/paper_write_switch/index.vue'),
            meta: {
              title: "去订正",
              showTab: false,
              requiresNestedView: true,
              // backPath: '/ai_percision/knowledge_graph_detail/worning_learning',
            },
          },
          {
            path: 'training_report_wenC',
            name: 'trainingReportWenC',
            component: () => import('@/views/ai_percision/training_report_wen/index.vue'),
            meta: {
              title: "训练详情",
              showTab: false,
              // backPath: '/ai_percision/knowledge_graph_detail/worning_learning',

              requiresNestedView: true
            },
          },
          {
            path: 'paper_write_switchJ',
            name: 'worningLearningJ',
            component: () => import('@/views/ai_percision/paper_write_switch/index.vue'),
            meta: {
              title: "举一反三",
              showTab: false,
              requiresNestedView: true,
              backPath: '/ai_percision/knowledge_graph_detail/worning_learning',
            },
          },
          {
            path: 'training_report',
            name: 'trainingReport',
            component: () => import('@/views/ai_percision/training_report/index.vue'),
            meta: {
              title: "训练报告",
              showTab: false,
              requiresNestedView: true
            },
          },
          {
            path: 'paper_analysis',
            name: 'paperAnalysis',
            component: () => import('@/views/ai_percision/paper_analysis/index.vue'),
            meta: {
              title: "试卷分析",
              showTab: false,
              requiresNestedView: true,
              backPath: '/ai_percision/knowledge_graph_detail/my_paper'
            },
          }
        ]
      },
      {
        path: 'knowledge_graph_detail_unit',
        name: 'KnowledgeGraphDetailUnit',
        component: () => import('@/views/ai_percision/knowledge_graph_detail/index.vue'),
        meta: {
          title: "单元测试",
          showTab: false,
        },
        children: [
          {
            path: 'paper_detailU',
            name: 'KnowledgeGraphTestDetailU',
            component: () => import('@/views/true_paper/true_paper_detail/index.vue'),
            meta: {
              title: "试卷详情",
              showTab: false,
              requiresNestedView: true
            },
          },
          {
            path: 'my_paperU',
            name: 'MyPaperU',
            component: () => import('@/views/ai_percision/my_paper/index.vue'),
            meta: {
              title: "我的试卷",
              showTab: false,
              requiresNestedView: true
            },
          },
          {
            path: 'paper_writeU',
            name: 'paperWriteU',
            component: () => import('@/views/ai_percision/paper_write/index.vue'),
            meta: {
              title: "试卷作答",
              showTab: false,
              requiresNestedView: true
            },
          }
        ]
      },
      {
        path: 'wkvideo',
        name: 'NoteWkvideo3',
        component: () => import('@/views/note/wkVideo.vue'),
        meta: {
          title: "知识点视频",
          showTab: false,
        },
        children: [
          {
            path: 'paper_write_switchJ3',
            name: 'worningLearningJ3',
            component: () => import('@/views/ai_percision/paper_write_switch/index.vue'),
            meta: {
              title: "举一反三",
              showTab: false,
              requiresNestedView: true,
              backPath: '/ai_percision/wkvideo',
            },
          },
        ]
      },
      {
        path: 'wkvideo2',
        name: 'NoteWkvideo4',
        component: () => import('@/views/note/wkVideo2.vue'),
        meta: {
          title: "知识点视频",
          showTab: false,
        },
        children: [
          {
            path: 'paper_write_switchJ4',
            name: 'worningLearningJ4',
            component: () => import('@/views/ai_percision/paper_write_switch/index.vue'),
            meta: {
              title: "举一反三",
              showTab: false,
              requiresNestedView: true,
              backPath: '/ai_percision/wkvideo2',
            },
          },
        ]
      },
      {
        path: 'entrance_assessment',
        name: 'EntranceAssessment',
        component: () => import('@/views/ai_percision/entrance_assessment/index.vue'),
        meta: {
          title: "入学测评",
          showTab: false
        },
        children: [
          {
            path: 'answer_questions',
            name: 'AnswerQuestions',
            component: () => import('@/views/ai_percision/entrance_assessment/answer_questions.vue'),
            meta: {
              title: "答题",
              showTab: false,
              requiresNestedView: true
            },
          },
          {
            path: 'doing_exercises',
            name: 'DoingExercises',
            component: () => import('@/views/ai_percision/entrance_assessment/doing_exercises.vue'),
            meta: {
              title: "答题测试",
              showTab: false,
              requiresNestedView: true
            },
          },
          {
            path: 'test_report',
            name: 'TestReport',
            component: () => import('@/views/ai_percision/entrance_assessment/test_report.vue'),
            meta: {
              title: "测评报告",
              showTab: false,
              requiresNestedView: true
            },
          },
          {
            path: 'test_questions',
            name: 'TestQuestions',
            component: () => import('@/views/ai_percision/entrance_assessment/test_questions.vue'),
            meta: {
              title: "查看试题",
              showTab: false,
              requiresNestedView: true
            },
          },
          {
            path: 'answer_record',
            name: 'AnswerRecord',
            component: () => import('@/views/ai_percision/entrance_assessment/answer_record.vue'),
            meta: {
              title: "答题记录",
              showTab: false,
              requiresNestedView: true
            },
          },
          {
            path: 'paper_write_switch_ea',
            name: 'PaperWriteSwitchEA',
            component: () => import('@/views/ai_percision/paper_write_switch/index.vue'),
            meta: {
              title: "订正错题",
              showTab: false,
              requiresNestedView: true
            },
          }
        ]
      },
      {
        path: 'final_question',
        name: 'FinalQuestion',
        component: () => import('@/views/ai_percision/finalQuestion/index.vue'),
        meta: {
          title: "压轴题专练",
          showTab: false,
          isFull: true,
          hideCrumb: true, // 是否隐藏面包屑
        },
        children: [
          {
            path: 'final_question_write',
            name: 'FinalQuestionWrite',
            component: () => import('@/views/ai_percision/paper_write_switch/index.vue'),
            meta: {
              title: "压轴题专练",
              showTab: false,
              requiresNestedView: true
            },
          },
          {
            path: 'final_training_report',
            name: 'FinalTrainingReport',
            component: () => import('@/views/ai_percision/training_report/index.vue'),
            meta: {
              title: "闯关报告",
              showTab: false,
              requiresNestedView: true
            },
          },
          {
            path: 'final__learning',
            name: 'FinalLearning',
            component: () => import('@/views/ai_percision/regarding_learning/index.vue'),
            meta: {
              title: "针对学习",
              showTab: false,
              requiresNestedView: true
            },
          },
          {
            path: 'final__record',
            name: 'FinalRecord',
            component: () => import('@/views/ai_percision/finalQuestion/components/finalRecord.vue'),
            meta: {
              title: "闯关记录",
              showTab: false,
              isFull: false,
              backPath: '/ai_percision/final_question/final_question_write',
              hideCrumb: false, // 是否隐藏面包屑
              requiresNestedView: true
            },
          },
        ]
      },
      {
        path: 'olympiad_question',
        name: 'OlympiadQuestion',
        component: () => import('@/views/ai_percision/olympiadQuestion/index.vue'),
        meta: {
          title: "奥数题专练",
          showTab: false,
          isFull: true,
          hideCrumb: true, // 是否隐藏面包屑
        },
        children: [
          {
            path: 'olympiad_question_write',
            name: 'OlympiadQuestionWrite',
            component: () => import('@/views/ai_percision/paper_write_switch/index.vue'),
            meta: {
              title: "奥数题专练",
              showTab: false,
              requiresNestedView: true
            },
          },
          {
            path: 'olympiad_training_report',
            name: 'OlympiadTrainingReport',
            component: () => import('@/views/ai_percision/training_report/index.vue'),
            meta: {
              title: "闯关报告",
              showTab: false,
              requiresNestedView: true
            },
          },
          {
            path: 'olympiad__learning',
            name: 'OlympiadLearning',
            component: () => import('@/views/ai_percision/regarding_learning/index.vue'),
            meta: {
              title: "针对学习",
              showTab: false,
              requiresNestedView: true
            },
          },
          {
            path: 'olympiad__record',
            name: 'OlympiadRecord',
            component: () => import('@/views/ai_percision/olympiadQuestion/components/finalRecord.vue'),
            meta: {
              title: "闯关记录",
              showTab: false,
              isFull: false,
              backPath: '/ai_percision/final_question/olympiad_question_write',
              hideCrumb: false, // 是否隐藏面包屑
              requiresNestedView: true
            },
          },
        ]
      },
      {
        path: 'knowledge_hotspot',
        name: 'KnowledgeHotspot',
        component: () => import('@/views/ai_percision/knowledge_hotspot/index.vue'),
        meta: {
          title: "知识点标热",
          showTab: false
        }
      },
      {
        path: 'basic_training',
        name: 'BasicTraining',
        component: () => import('@/views/ai_percision/basic_training/index.vue'),
        meta: {
          title: "基础训练",
          showTab: false,
        },
      },
      {
        path: 'answer_training',
        name: 'AnswerTraining',
        component: () => import('@/views/ai_percision/basic_training/answer_training.vue'),
        meta: {
          title: "答题训练",
          showTab: false,
        },
      },
      {
        path: 'foundation_training',
        name: 'FoundationTraining',
        component: () => import('@/views/ai_percision/basic_training/training.vue'),
        meta: {
          title: "基础训练1",
          showTab: false,
        },
      },
      {
        path: 'foundation_report',
        name: 'FoundationReport',
        component: () => import('@/views/ai_percision/basic_training/report.vue'),
        meta: {
          title: "基础训练1",
          showTab: false,
        },
      },
    ]
  },

  // {
  //   path: '/ai_companion',
  //   name: 'AiCompanion',
  //   component: Layout,
  //   redirect: "/ai_companion/ai_companion_page",
  //   meta: {
  //     title: "AI伴学",
  //     icon: 'ai-companion',
  //   },
  //   children: [
  //     {
  //       path: 'ai_companion_page',
  //       name: 'AiCompanionPage',
  //       component: () => import('@/views/ai_companion/ai_companion_page/index.vue'),
  //       meta: {
  //         title: "AI伴学详情",
  //         showTab: true,
  //       },
  //     }
  //   ]
  // },
  // knowledgeTitle
  {
    path: '/teach_room',
    name: 'TeachRoom',
    component: Layout2,
    redirect: "/teach_room/index",
    meta: {
      title:'名师知识点',     //小学初中  名师知识点  高中  知识点视频
      icon: 'teachvid',
    },
    children: [
      {
        path: 'index',
        name: 'TeachRoomIndex',
        component: () => import('@/views/teach_room/index.vue'),
        meta: {
          title: "首页",
          showTab: true,
          keepAlive: false,
          isFull: true
        },
      },
      {
        path: 'introduce',
        name: 'TeachRoomIntroduce',
        component: () => import('@/views/teach_room/introduce.vue'),
        meta: {
          title: "介绍",
          showTab: false,
          keepAlive: false,
          isFull: true
        },
      },
      {
        path: 'teach_list',
        name: 'TeachRoomTeachList',
        component: () => import('@/views/teach_room/teachList.vue'),
        meta: {
          title: "章节列表",
          showTab: false,
          keepAlive: true,
          isFull: true
        },
      },
      {
        path: 'teach_view',
        name: 'TeachRoomTeachView',
        component: () => import('@/views/teach_room/teachView.vue'),
        meta: {
          title: "知识点概述",
          showTab: false,
          keepAlive: true,
          isFull: true
        },
      },
      {
        path: 'teach_collect',
        name: 'TeachRoomTeachCollect',
        component: () => import('@/views/teach_room/teachCollect.vue'),
        meta: {
          title: "我的收藏",
          showTab: false,
          keepAlive: true,
          isFull: true
        },
      },
      {
        path: 'teach_video',
        name: 'TeachRoomTeachVideo',
        component: () => import('@/views/teach_room/teachVideo.vue'),
        meta: {
          title: "知识点视频",
          showTab: false,
          keepAlive: true,
          isFull: true
        },
      },
      {
        path: 'teach_video2',
        name: 'TeachRoomTeachVideo2',
        component: () => import('@/views/teach_room/teachVideo2.vue'),
        meta: {
          title: "知识点视频",
          showTab: false,
          keepAlive: true,
          isFull: true
        },
      }
    ]
  },

  // 名师同步学
    {
    path: '/synchronous',
    name: 'Synchronous',
    component: Layout,
    redirect: "/synchronous/index",
    meta: {
      title: "名师同步学",
      icon: 'sync-study',
      showTab: true
    },
    children: [
      {
        path: 'index',
        name: 'SynchronousIndex',
        component: () => import('@/views/synchronous/index.vue'),
        meta: {
          title: "名师同步学",
          showTab: true,
          keepAlive: false
        },
      },
      {
        path: 'introduce',
        name: 'Introduce',
        component: () => import('@/views/synchronous/introduce.vue'),
        meta: {
          title: "介绍",
          showTab: false,
          keepAlive: false,
          isFull: true
        },
      },
      {
        path: 'synchronous_list',
        name: 'SynchronousList',
        component: () => import('@/views/synchronous/synchronousList.vue'),
        meta: {
          title: "章节列表",
          showTab: true,
          keepAlive: true,
          isFull: true
        },
      },
      {
        path: 'synchronous_video',
        name: 'SynchronousVideo',
        component: () => import('@/views/synchronous/synchronousVideo.vue'),
        meta: {
          title: "同步学视频",
          showTab: false,
          keepAlive: true,
          isFull: true
        },
      },
      {
        path: 'synchronous_collect',
        name: 'synchronousCollect',
        component: () => import('@/views/synchronous/synchronousCollect.vue'),
        meta: {
          title: "我的收藏",
          showTab: false,
          keepAlive: true,
          isFull: true
        },
      },
    ]
  },

  {
    path: '/point_room',
    name: 'PointRoom',
    component: Layout2,
    redirect: "/point_room/index",
    meta: {
      title: "知识点视频",
      icon: 'teachvid',
    },
    children: [
      {
        path: 'index',
        name: 'PointRoomIndex',
        component: () => import('@/views/point_room/index.vue'),
        meta: {
          title: "首页",
          showTab: true,
          keepAlive: false,
          isFull: true
        },
      },
      {
        path: 'introduce',
        name: 'PointRoomIntroduce',
        component: () => import('@/views/point_room/introduce.vue'),
        meta: {
          title: "介绍",
          showTab: false,
          keepAlive: false,
          isFull: true
        },
      },
      {
        path: 'point_list',
        name: 'PointRoomTeachList',
        component: () => import('@/views/point_room/teachList.vue'),
        meta: {
          title: "章节列表",
          showTab: false,
          keepAlive: true,
          isFull: true
        },
      },
      {
        path: 'point_view',
        name: 'PointRoomTeachView',
        component: () => import('@/views/point_room/teachView.vue'),
        meta: {
          title: "知识点概述",
          showTab: false,
          keepAlive: true,
          isFull: true
        },
      },
      {
        path: 'point_collect',
        name: 'PointRoomTeachCollect',
        component: () => import('@/views/point_room/teachCollect.vue'),
        meta: {
          title: "我的收藏",
          showTab: false,
          keepAlive: true,
          isFull: true
        },
      },
      {
        path: 'point_video',
        name: 'PointRoomTeachVideo',
        component: () => import('@/views/point_room/teachVideo.vue'),
        meta: {
          title: "知识点视频",
          showTab: false,
          keepAlive: true,
          isFull: true
        },
      },
      {
        path: 'point_video2',
        name: 'PointRoomTeachVideo2',
        component: () => import('@/views/point_room/teachVideo2.vue'),
        meta: {
          title: "知识点视频",
          showTab: false,
          keepAlive: true,
          isFull: true
        },
      }
    ]
  },
  {
    path: '/true_paper',
    name: 'TruePaper',
    component: Layout,
    redirect: "/true_paper/true_paper_page",
    meta: {
      title: "真题试卷",
      icon: 'true-paper',
    },
    children: [
      {
        path: 'true_paper_page',
        name: 'TruePaperPage',
        component: () => import('@/views/true_paper/true_paper_page/index.vue'),
        meta: {
          title: "试卷列表",
          showTab: true,
        },
        children: [
          {
            path: 'true_paper_detail',
            name: 'TruePaperDetail',
            component: () => import('@/views/true_paper/true_paper_detail/index.vue'),
            meta: {
              title: "试卷详情",
              showTab: false,
              requiresNestedView: true
            },
          },
          {
            path: 'paper_write_switchT',
            name: 'TruePaperWrite',
            component: () => import('@/views/ai_percision/paper_write_switch/index.vue'),
            meta: {
              title: "试卷作答",
              showTab: false,
              requiresNestedView: true
            },
          },
          {
            path: 'assignment',
            name: 'Assignment',
            component: () => import('@/views/ai_percision/paper_write_switch/assignment.vue'),
            meta: {
              title: "作答",
              showTab: true,
              requiresNestedView: true
            },
          },
          // {
          //   path: 'assignment-report',
          //   name: 'AssignmentReport',
          //   component: () => import('@/views/ai_percision/paper_write_switch/assignment-1.vue'),
          //   meta: {
          //     title: '答题报告',
          //     keepAlive: false
          //   }
          // },
          {
            path: 'analysis',
            name: 'Analysis',
            component: () => import('@/views/ai_percision/paper_write_switch/analysis.vue'),
            meta: {
              title: "分析",
              showTab: true,
              requiresNestedView: true
            },
          },
          {
            path: 'training_report_wenT',
            name: 'trainingReportWenT',
            component: () => import('@/views/ai_percision/training_report_wen/index.vue'),
            meta: {
              title: "训练详情",
              showTab: false,
              backPath: '/true_paper/true_paper_page',
              requiresNestedView: true
            },
          },
          {
            path: 'paper_analysisT',
            name: 'paperAnalysisT',
            component: () => import('@/views/ai_percision/paper_analysis/index.vue'),
            meta: {
              title: "试卷分析",
              showTab: false,
              requiresNestedView: true,
              backPath: '/true_paper/true_paper_page'
            },
          }
        ]
      },
      {
        path: 'my_paperT',
        name: 'MyPaperT',
        component: () => import('@/views/ai_percision/my_paper/index.vue'),
        meta: {
          title: "我的试卷",
          showTab: false
        },
        children: [
          {
            path: 'true_paper_detailM',
            name: 'TruePaperDetailM',
            component: () => import('@/views/true_paper/true_paper_detail/index.vue'),
            meta: {
              title: "试卷详情",
              showTab: false,
              requiresNestedView: true,
              backPath: '/true_paper/my_paperT'
            },
          },
          {
            path: 'paper_analysisTM',
            name: 'paperAnalysisTM',
            component: () => import('@/views/ai_percision/paper_analysis/index.vue'),
            meta: {
              title: "试卷分析",
              showTab: false,
              requiresNestedView: true,
              backPath: '/true_paper/my_paperT'
            },
          }
        ],
      },
    ]
  },
  {
    path: '/note',
    name: 'Note',
    component: Layout2,
    redirect: "/note/note_list",
    meta: {
      title: "AI错题本",
      icon: 'note',
    },
    children: [
      {
        path: 'note_list',
        name: 'NoteList',
        component: () => import('@/views/note/noteList.vue'),
        meta: {
          title: "错题本-列表",
          showTab: true,
          isFull: true,
          keepAlive: true
        }
      },
      {
        path: 'paper_write_switchAC',
        name: 'worningLearningAC',
        component: () => import('@/views/ai_percision/paper_write_switch/index.vue'),
        meta: {
          title: "去订正",
          showTab: false,
          requiresNestedView: true,
          backPath: '/note/note_list',
        },
      },
      {
        path: 'training_report_wenAJ',
        name: 'trainingReportWenAJ',
        component: () => import('@/views/ai_percision/training_report_wen/index.vue'),
        meta: {
          title: "训练详情",
          showTab: false,
          backPath: '/note/note_list',
          requiresNestedView: true
        },
      },
      {
        path: 'paper_write_switchAJ',
        name: 'worningLearningAJ',
        component: () => import('@/views/ai_percision/paper_write_switch/index.vue'),
        meta: {
          title: "举一反三",
          showTab: false,
          requiresNestedView: true,
          backPath: '/note/note_list',
        },
      },
      {
        path: 'note_help',
        name: 'NoteHelp',
        component: () => import('@/views/note/noteHelp.vue'),
        meta: {
          title: "拍搜结果",
          showTab: false,
          isFull: true,
          keepAlive: true
        },
      },
      {
        path: 'wkvideo',
        name: 'NoteWkvideo',
        component: () => import('@/views/note/wkVideo.vue'),
        meta: {
          title: "知识点视频",
          showTab: false,
          isFull: true,
          keepAlive: true
        },
      },
      {
        path: 'wkvideo2',
        name: 'NoteWkvideo2',
        component: () => import('@/views/note/wkVideo2.vue'),
        meta: {
          title: "知识点视频",
          showTab: false,
          isFull: true,
          keepAlive: true
        },
      }
    ]
  },
  {
    path: '/user',
    name: 'User',
    redirect: "/user_add",
    children: [
      {
        path: 'user_add',
        name: 'UserAdd',
        component: () => import('@/views/user/userAdd.vue'),
        meta: {
          title: "学习用户-添加",
          showTab: false,
          isFull: true,
        },
      },
      {
        path: 'user_list',
        name: 'UserList',
        component: () => import('@/views/user/userList.vue'),
        meta: {
          title: "学习用户-列表",
          showTab: false,
          isFull: true,
        },
      },
      {
        path: 'user_coin',
        name: 'UserCoin',
        component: () => import('@/views/user/userCoin.vue'),
        meta: {
          title: "我的积分-列表",
          hideTab: true,
          isFull: true
        },
      }
    ]
  },
  {
    path: '/user_coin2',
    name: 'UserCoin2',
    redirect: "/user/user_coin",
    meta: {
      title: "我的积分",
      icon: 'integral',
      isFull: true
    },
  },
  {
    path: '/academic_report',
    name: 'AcademicReport',
    redirect: "/academic_report/academic_report_page",
    component: Layout,
    meta: {
      title: "学情报告",
      icon: 'academic',
    },
    children: [
      {
        path: 'academic_report_page',
        name: 'AcademicReportPage',
        component: () => import('@/views/academic_report/academic_report_page/index.vue'),
        meta: {
          title: "报告详情",
          isFull: true,
          hideCrumb: true, // 是否隐藏面包屑
        },
        childern: [
          {
            path: 'paper_write_switchA',
            name: 'worningLearningA',
            component: () => import('@/views/ai_percision/paper_write_switch/index.vue'),
            meta: {
              title: "去订正",
              showTab: false,
              requiresNestedView: true,
              backPath: '/academic_report/academic_report_page',
            },
          },
        ]
      },
      {
        path: 'training_report_wenA',
        name: 'trainingReportWenA',
        component: () => import('@/views/ai_percision/training_report_wen/index.vue'),
        meta: {
          title: "训练详情",
          showTab: false,
          backPath: '/academic_report/academic_report_page',
        },
      },
      {
        path: 'training_reportA',
        name: 'trainingReportA',
        component: () => import('@/views/ai_percision/training_report/index2.vue'),
        meta: {
          title: "训练报告",
          showTab: false,
          backPath: '/academic_report/academic_report_page',
        },
      },
      {
        path: 'regarding_learningA',
        name: 'regardingLearningA',
        component: () => import('@/views/ai_percision/regarding_learning/index.vue'),
        meta: {
          title: "针对学习",
          showTab: false,
          backPath: '/academic_report/academic_report_page',
        },
      },
      {
        path: 'knowledge_graph_detailA',
        name: 'KnowledgeGraphDetailA',
        component: () => import('@/views/ai_percision/knowledge_graph_detail/index.vue'),
        meta: {
          title: "知识图谱详情",
          backPath: '/academic_report/academic_report_page',
          showTab: false,
        },
      },
    ]
  },
]

const router = createRouter({
  history: createWebHistory(),
  routes,
})

router.beforeEach((to, from, next) => {
  const authStore = useUserStoreHook()
  if (to.meta.requiresAuth && !authStore.token) {
    next({ name: 'Login' })
  } else {
    const sign = localStorage.getItem('isPopSaveDialog')
    if (from.path.includes('paper_write_switch') && sign == '1') {
      // 先关闭所有可能存在的 MessageBox
      ElMessageBox.close()
      ElMessageBox.confirm("<p>当前练习未完成，确定要退出吗？</p><p class='el-message-box-tips'>（退出后自动为您保存当前练习进度）</p>", '退出', {
        confirmButtonText: '继续答题',
        cancelButtonText: '退出',
        dangerouslyUseHTMLString: true,
        distinguishCancelAndClose: true,
        center: true
      }).then(() => {
        router.replace(from.fullPath) // 确保地址栏不变
        next(false)
        // next()
      }).catch((action: Action) => {
        // 区分取消还是关闭
        if (action === 'close') {
          router.replace(from.fullPath) // 确保地址栏不变
          next(false)
        } else {
          // 保存作答记录逻辑-点退出
          const matched: any = from.matched
          if (matched.length == 3) {
            //我的试卷-试卷详情-开始作答
            const name2 = matched[1].name == 'TruePaperPage',
              name3 = matched[2].name == 'TruePaperWrite'
            //纠错-知识点视频-举一反三
            const jyname1 = matched[1].name == 'NoteWkvideo4' || matched[1].name == 'NoteWkvideo3',
              jyname2 = matched[2].name == 'worningLearningJ4'
            if (name2 && name3) {
              localStorage.setItem('isPopSaveDialog', '0')
              router.go(-1)
            } else if (jyname1 && jyname2) {
              localStorage.setItem('isPopSaveDialog', '0')
              router.go(-1)
            } else {
              next()
            }
          } else {
            next()
          }
        }
      })
    } else {
      next()
    }
  }
})

export default router
