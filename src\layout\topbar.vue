<template>
  <div class="top-bar flex-center">
    <div class="top-bar-content flex-center">
      <div class="top-bar-content-left flex-center">
        <div class="top-bar-content-left-child flex-center">
          <!-- <span class="green-text">选择您的学生: </span> -->
          <img :src="state.usersNow.avatar" v-if="state.usersNow.avatar" @click="goUrl('UserList')" />
          <img src="@/assets/img/login/logo.png" v-else />
          <div class="top-bar-content-left-select">
            <div class="score-ranking" @click="goUrl('UserCoin')">
              积分排名：{{state.myRank.ranking?'第'+state.myRank.ranking+'名':'未上榜'}}</div>
              <div class="child-name-select" @click="goUrl('UserList')">{{ state.usersNow.nickName || '' }}  <span style="padding-left: 10px;">({{ state.usersNow.gradeName }})</span> </div>
            <el-popover placement="bottom" width="100%" @show="popoverShow" @hide="popoverHide"
              :v-model:visible="arrowV" popper-class="popper-child" trigger="click" v-if="false">
              <template #reference>
                <div class="child-name-select">{{ state.usersNow.nickName || '' }}
                  <!-- <div class="child-name-select-arrow" :class="arrowV?'child-name-select-arrow-up':''"></div> -->
                </div>
              </template>
              <div class="child-box">
                <!-- 当前学生 -->
                <template v-for="(item,i) in state.list" :key="item.learnId">
                  <template v-if="item.learnId == state.usersNow.learnId">
                    <div class="child-item" :class="item.learnId == state.usersNow.learnId?'child-item-selected':''">
                      <img :src="item.avatar" v-if="item.avatar" />
                      <img src="@/assets/img/login/logo.png" v-else />
                      <div class="child-item-info">
                        <span>{{ item.nickName || '' }}</span>
                        <span>年级：{{ item.gradeName }}</span>
                        <span>ID：{{ item.learnNo }}</span>
                      </div>
                      <div class="child-item-btn">
                        <div class="child-item-btn-text" @click="changeId(i)"
                          v-if="item.learnId != state.usersNow.learnId">切换学生 ></div>
                        <div class="child-item-btn-text" v-else></div>
                        <div class="child-item-btn-current" v-if="item.learnId == state.usersNow.learnId">当前学生</div>
                      </div>
                    </div>
                  </template>
                </template>
                <!-- 非当前 -->
                <template v-for="(item,i) in state.list" :key="item.id">
                  <template v-if="item.learnId != state.usersNow.learnId">
                    <div class="child-item" :class="item.learnId == state.usersNow.learnId?'child-item-selected':''">
                      <img :src="item.avatar" v-if="item.avatar" />
                      <img src="@/assets/img/login/logo.png" v-else />
                      <div class="child-item-info">
                        <span>{{ item.nickName || '' }}</span>
                        <span>年级：{{ item.gradeName }}</span>
                        <span>ID：{{ item.learnNo }}</span>
                      </div>
                      <div class="child-item-btn">
                        <div class="child-item-btn-text" @click="changeId(i)"
                          v-if="item.learnId != state.usersNow.learnId">切换学生 ></div>
                        <div class="child-item-btn-text" v-else></div>
                        <div class="child-item-btn-current" v-if="item.learnId == state.usersNow.learnId">当前学生</div>
                      </div>
                    </div>
                  </template>
                </template>
                <div class="info-parents" @click="goUrl('UserList')">进入家长信息 ></div>
              </div>
            </el-popover>
          </div>
        </div>
      </div>
      <div style="display: flex;">
        <!-- 签到 -->
        <img src="@/assets/img/layout/sign-in.png" class="sign-in" alt="qiandao" @click="signIn"
          v-if="!state.isDaySign" />
        <!-- 家长信息 -->
        <div class="top-bar-content-right flex-center">
          <div class="top-bar-content-right-content">
            <div class="position-box" @click="goUrl('UserList')">
              <img :src="state.userInfo.avatar" v-if="state.userInfo.avatar">
              <img src="@/assets/img/login/logo.png" v-else>
              <span
                v-if="state.memberInfo">{{state.memberInfo.memberType=='vip'?'VIP':state.memberInfo.memberType=='svip'?'SVIP':state.memberInfo.memberType}}</span>
            </div>
            <!-- state.userInfo.name || -->
            <span class="nike-name" @click="goUrl('UserList')">{{state.userInfo.phone2}}</span>
            <div class="exit-box">
              <img class="exit-img" src="@/assets/img/layout/exit.png" alt="qiandao">
              <span class="exit-text" @click="quitShow">退出</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- 退出弹窗 -->
  <div v-if="state.isQuit">
    <div class="alert_bg"></div>
    <div class="alert_box">
      <div class="alert_inner">
        <div class="alert_top">
          <div class="alert_h1">提示</div>
          <img src="@/assets/img/user/close.svg" class="alert_x" @click="quitHide" />
        </div>
        <div class="alert_wrap">
          <div class="alert_tit">确定要退出当前账号吗？</div>
          <div class="alert_btns">
            <div class="alert_quit" @click="quitHide">取消</div>
            <div class="alert_ok" @click="useUserStore().logout()">确定</div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- 积分弹窗 -->
  <coinAlert :show="state.jfShow" num="5" source="0" :isAjax="false" @close="signHide"></coinAlert>
</template>

<script lang="ts" setup>
  import { reactive, ref, onMounted } from 'vue';
  import router from '@/router/index'
  import { useUserStore } from "../store/modules/user"
  import { userGetAllApi, integralListApi, getUserApi, userVipInfoApi, everydaySignInBoolApi, everydaySignInApi, userDefaultApi } from "../api/user"
  import { gradeNameList } from '../utils/user/enum'
  import { ElMessage } from "element-plus"
  import coinAlert from "@/views/components/coinAlert/index.vue"

  onMounted(() => {
    getList()
    integralList()
    getUserInfo()
    // userVipInfo()
    signInBool()
  })

  let learnNow = localStorage.learnNow ? JSON.parse(localStorage.learnNow) : ''
  const state : any = reactive({
    list: [],
    usersNow: '',
    learnId: learnNow?.learnId || '',
    isQuit: false,
    myRank: {
      "learnUserId": "",
      "id": "",
      "orgId": null,
      "userName": "",
      "gradeId": 0,
      "avatar": "",
      "ranking": 0,
      "totalIntegral": 0
    },
    userId: localStorage.sysUserId || '',
    userInfo: localStorage.userInfo ? JSON.parse(localStorage.userInfo) : '',
    memberInfo: '',
    isDaySign: localStorage.isDaySign == '1' ? 1 : 0,
    jfShow: false
  })

  //学生列表
  const getList = () => {
    let learnNow = '', learnUsers = ''
    let deviceLen = 0
    userGetAllApi().then((res : any) => {
      const arr:any = [res.data]
      if (arr.length) {
        for (const i of arr) {
          //获取小程序用户数
          deviceLen++
          i.gradeName = gradeNameList[i.gradeId]
          if (i.isDefault) {
            learnNow = i
          }
        }
        learnUsers = arr
        if (!learnNow) {
          // 默认第一个
          arr[0].isDefault = true
          learnNow = arr[0]
        }
      }
      state.list = learnUsers
      state.usersNow = learnNow
      //缓存学生列表
      useUserStore().setlearnNow(learnNow)
      learnUsers = JSON.stringify(learnUsers)
      useUserStore().learnUsers = learnUsers
      localStorage.learnUsers = learnUsers
    })
  }

  //切换学生
  const changeId = (i : any) => {
    ElMessage.success('切换成功')
    const arr = state.list
    arr[i].isDefault = true
    const { versions, avatar, childId, gradeId, learnId, region, nickName, sex, learnNo } = arr[i]
    const versions2 : any = []
    //处理教材
    for (const n of versions) {
      versions2.push({
        subject: n.subject,
        bookId: n.bookId
      })
    }
    const param = {
      versions: versions2,
      avatar: 'education' + avatar.split('/education')[1],
      nickName,
      childId,
      gradeId,
      gradeNum: learnNo,
      sex,
      region,
      learnId,
      isDefault: true
    }
    userDefaultApi(param).then(() => {
      //缓存默认用户
      const list = state.list
      for (const x of list) {
        x.isDefault = false
        if (x.learnId == learnId) {
          x.isDefault = true
        }
      }
      state.list = list
      state.usersNow = arr[i]
      const learnUsers = JSON.stringify(list)
      useUserStore().setlearnNow(arr[i])
      useUserStore().learnUsers = learnUsers
      localStorage.learnUsers = learnUsers
      //刷新页面
      setTimeout(() => {
        location.reload()
      }, 200)
    })
  }

  //下拉菜单
  const arrowV = ref(false)
  const popoverHide = () => {
    // arrowV.value = false
  }
  const popoverShow = () => {
    // arrowV.value = true
  }

  //跳转路由
  const goUrl = (name : string) => {
    router.push({ name })
  }

  //积分排名
  const integralList = () => {
    const data = {
      learnUserId: state.learnId,
      isData: 0
    }
    integralListApi(data)
      .then((res : any) => {
        if (res?.data?.learnUserIntegralVoBuilder) {
          state.myRank = res?.data?.learnUserIntegralVoBuilder
        }
      })
  }

  //家长信息-平板没家长修改这块
  const getUserInfo = () => {
    let phone = state.userInfo.phone
    //手机号脱敏
    state.userInfo.phone2 = phone.substr(0, 3) + "****" + phone.substr(7, 4)
    // getUserApi() .then((res : any) => { })
  }
  //会员信息
  const userVipInfo = () => {
    let param = {
      userId: state.userId
    }
    userVipInfoApi(param).then((res : any) => {
      let memberInfo = res.data || '1'//自习室默认有会员
      state.memberInfo = memberInfo
      if (memberInfo) {
        memberInfo = JSON.stringify(memberInfo)
      }
      localStorage.memberInfo = memberInfo
      useUserStore().memberInfo = memberInfo
    })
  }

  //签到判断
  const signInBool = () => {
    if (!localStorage.isDaySign) {
      let param = {
        learnUserId: state.learnId
      }
      everydaySignInBoolApi(param).then((res : any) => {
        let data = res.data.everydaySignInBool ? 1 : 0
        localStorage.isDaySign = data
      })
    }
  }

  //签到
  const signIn = () => {
    let param : any = {
      learnUserId: state.learnId
    }
    everydaySignInApi(param).then(() => {
      state.isDaySign = 1
      localStorage.isDaySign = '1'
      state.jfShow = true
    })
  }
  // 签到回调
  const signHide = () => {
    state.jfShow = false
    state.isDaySign = 1
    localStorage.isDaySign = '1'
  }

  //退出登录
  const quitHide = () => {
    state.isQuit = false
  }
  const quitShow = () => {
    state.isQuit = true
  }
</script>
<style lang="scss" scoped>
  .none{
    display: none !important;
  }

  .flex-center {
    display: flex;
    align-items: center;
  }

  .top-bar {
    width: 100%;
    justify-content: center;
    height: 4.375rem;
    background-image: url(../assets/img/layout/top-bg.png);
    background-size: cover;
    background-repeat: no-repeat;
    background-color: #fff;

    &-content {
      width: 81.25rem;
      height: 4.375rem;
      justify-content: space-between;

      &-left {
        &-child {
          width: 18.75rem;

          .green-text {
            font-size: .875rem;
            font-weight: 700;
            color: rgb(0, 156, 127);
          }

          img {
            width: 2.75rem;
            height: 2.75rem;
            border-radius: 50%;
            border: .125rem solid rgb(0, 201, 163);
            margin-right: .625rem;
            margin-left: .625rem;
            cursor: pointer;
          }

          .child-name-select {
            position: relative;
            cursor: pointer;
            font-size: .875rem;
            width: max-content;
            font-weight: 400;

            &-arrow {
              position: absolute;
              top: .4375rem;
              right: -1.5625rem;
              width: .875rem;
              height: .875rem;
              background-image: url(@/assets/img/layout/arrow-down.png);
              background-size: 100%;
              background-repeat: no-repeat;
            }

            .child-name-select-arrow-up {
              background-image: url(@/assets/img/layout/arrow-up.png);
            }
          }
        }

        &-score {
          width: 15.375rem;
          height: 2.375rem;
          border: .0625rem solid rgb(153, 153, 153);
          border-radius: 1.375rem;
          margin-left: 4.5625rem;
          padding: .4375rem;
          box-sizing: border-box;

          .score-img {
            width: 1.5rem;
            height: 1.5rem;
          }

          span {
            font-size: 1rem;
            font-weight: 700;
            color: rgb(247, 152, 52);
          }

          .rank-box {
            width: 3.1875rem;
            height: 1.125rem;
            padding: 0 .375rem;
            border-radius: .5625rem;
            text-align: center;
            box-sizing: border-box;
            background: linear-gradient(161.22deg, rgb(255, 206, 57) 27.465%, rgb(255, 149, 36) 79.194%);
            color: rgb(255, 255, 255);
            font-size: .75rem;
            font-weight: 700;
            margin-left: .375rem;
            margin-right: .75rem;
          }

          .sign-img {
            width: 2rem;
            height: 2rem;
            margin-left: .75rem;
          }
        }
      }

      .sign-in {
        width: 4.875rem;
        height: 2rem;
        margin-right: 2.5rem;

        &:hover {
          cursor: pointer;
        }
      }

      &-right {
        width: 17.5625rem;
        height: 2.625rem;
        border: .0625rem solid rgb(0, 201, 163);
        border-radius: 1.375rem;
        justify-content: center;

        &-content {
          width: 17rem;
          height: 2.25rem;
          line-height: 2.25rem;
          border-radius: 1.125rem;
          background: rgb(0, 201, 163);
          color: rgb(255, 255, 255);
          position: relative;

          .position-box {
            cursor: pointer;
            position: absolute;
            top: -0.625rem;
            left: -0.625rem;

            img {
              width: 3.125rem;
              height: 3.125rem;
              border: .125rem solid rgb(0, 201, 163);
              border-radius: 50%;
            }

            span {
              position: absolute;
              width: 2.125rem;
              height: .9375rem;
              border-radius: 1.125rem;
              background: linear-gradient(150.75deg, rgb(54, 226, 194) 14.179%, rgb(0, 183, 208) 81.458%);
              font-size: .625rem;
              line-height: .9375rem;
              font-weight: 700;
              text-align: center;
              top: 2.5rem;
              left: .625rem;
            }
          }

          .nike-name {
            cursor: pointer;
            font-size: 1rem;
            font-weight: 700;
            width: 9.375rem;
            display: inline-block;
            margin-left: 3.4375rem;
          }

          .exit-box {
            display: inline-flex;
            align-items: center;
            cursor: pointer;

            .exit-img {
              width: .875rem;
              height: .875rem;
            }

            .exit-text {
              font-size: .875rem;
              margin-left: .3125rem;

            }
          }
        }
      }

    }
  }

  .score-ranking {
    padding: 0 .3125rem;
    height: 1.125rem;
    border-radius: .5625rem;
    background: linear-gradient(161.2deg, #ffce39 0%, #ff9524 100%);
    color: #ffffff;
    font-size: .75rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: .25rem;

    &:hover {
      cursor: pointer;
    }
  }

  /* 退出弹窗 */
  .alert_bg {
    z-index: 50;
    background: rgba(0, 0, 0, .7);
    position: fixed;
    width: 100%;
    height: 100vh;
    top: 0;
    left: 0;
  }

  .alert_box {
    z-index: 99;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .alert_inner {
    width: 31.25rem;
    border-radius: 1.25rem;
    background: #ffffff;
    box-sizing: border-box;
    padding: 0 1.25rem;
  }

  .alert_top {
    float: left;
    width: 100%;
    height: 3.375rem;
    border-bottom: .0625rem solid #eee;
  }

  .alert_h1 {
    float: left;
    width: 100%;
    line-height: 3.375rem;
    text-align: center;
    color: #2a2b2a;
    font-size: 1rem;
    font-weight: bold;
  }

  .alert_x {
    float: right;
    width: .9375rem;
    height: .9375rem;
    padding: 1.1875rem;
    margin: -3.125rem -1.125rem 0 0;
  }

  .alert_x:hover {
    cursor: pointer;
  }

  .alert_wrap {
    width: 100%;
    float: left;
    display: flex;
    align-items: center;
    flex-flow: column;
  }

  .alert_tit {
    line-height: 1.3125rem;
    color: #2a2b2a;
    font-size: 1rem;
    margin: 2.4375rem 0 1rem;
  }

  .alert_btns {
    display: flex;
    margin: 1.25rem 0 2.1875rem;
  }

  .alert_btns div {
    width: 7.625rem;
    line-height: 2.375rem;
    text-align: center;
    border-radius: 1.1875rem;
    font-size: 1rem;
  }

  .alert_btns div:hover {
    cursor: pointer;
  }

  .alert_quit {
    color: #666666;
    background: #f5f5f5;
  }

  .alert_ok {
    color: #DD2A2A;
    background: #fee9e9;
    margin: 0 0 0 2.125rem;
  }
</style>
