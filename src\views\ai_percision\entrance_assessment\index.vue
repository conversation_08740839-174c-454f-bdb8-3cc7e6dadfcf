<template>
    <div class="graph-content">
       <div class="left">
        <subjectSelect :selected="subject" @setSubject="setSubject" />
        </div>
        <div class="entrance-assessment-container" >
            <!-- 头部信息区域 -->
            <div class="header-banner">
                <div class="header-title">
                  <img class="exchange" @click="onChange" src="@/assets/img/entranceAssessment/dqjc.png" />
                    <span class="title-box">当前教材：</span>
                    {{ bookVersionName }}
                    <div class="switch" @click="onModify"><img class="exchange" @click="onChange" src="@/assets/img/entranceAssessment/jiaoh.png" /><span>切换教材</span></div>
                    <div class="banner-tipss">欢迎你，<span style="font-size: 14px;color: rgba(0, 201, 163, 1);font-weight: 600;">{{ learnUsers[0].nickName }} </span> 同学，请选择您的学习模式：</div>
                </div>
                <div class="pattern" style="display: flex;flex-wrap:wrap">
                  <div class="pattern-list" v-for="(val,index) in learningNewModes" :key="index" >
                    <div class="top-tps"> {{ val.tips }}</div>
                    <div class="learn">
                      <span>{{ val.description }}</span>
                      <div class="start-learn" @click="selectMode(val)"> 开始学习</div>
                    </div>
                    <div class="prompt" v-if="val.isPrompt">
                      <div class="prompt-top">
                        <img src="@/assets/img/entranceAssessment/books.png" alt="">
                        <span>正在学习：</span>
                        <div class="rig-cha" @click="val.isPrompt = false"><img src="@/assets/img/entranceAssessment/cha.png" alt=""></div>
                      </div>
                      <div class="prompt-title">{{ chapterType }}{{ chapterName }}</div>
                      <div class="continue">继续学习 ></div> 
                    </div>
                  </div>
                </div>   
            </div>

            <!-- 学习模式选择 -->
            <div class="learning-modes">
              <div class="top-nav">
                <img src="@/assets/img/entranceAssessment/ceyice.png" alt="">
                <div class="test">测一测</div>
                <p class="banner-subtitle isp" @click="showAssessmentRecord">查看测评记录 ></p>
                <div class="rig-introduction">单元测评 ｜ 期中测评 ｜ 期末测评</div>
              </div>
              <div class="measure-ct">
                <div class="measure-list">
                  <div class="measure-tt">单元测评</div>
                  <el-select v-model="selectedUnit" placeholder="第一单元：名师配新名称" class="unit-select" style="width: 80%;margin-left: 40px;">
                      <el-option
                      v-for="unit in unitList"
                      :key="unit.id"
                      :label="unit.name"
                      :value="unit.id"
                      />
                      <el-option
                      v-if="unitList.length === 0"
                      label="暂无数据"
                      value=""
                      disabled
                      />
                  </el-select>
                  <el-button type="primary" class="measure-btn" :disabled="unitList.length === 0" @click="showAssessmentRange(1)">开始测评</el-button>
                </div>
                <div class="measure-list">
                  <div class="measure-tt">期中测评</div>
                  <span >针对期中考试的常考知识点进行测评</span>
                  <el-button type="primary"
                          class="measure-btn"
                          :disabled="!assessmentData.midterm.available"
                          @click="showAssessmentRange(2)">开始测评</el-button>
                </div>
                <div class="measure-list">
                  <div class="measure-tt">期末测评</div>
                  <span>针对整学期常考知识点进行测评</span>
                  <el-button type="primary"
                          class="measure-btn"
                          :disabled="!assessmentData.final.available"
                          @click="showAssessmentRange(3)">开始测评</el-button>
                </div>
              </div>
            </div>
        </div>
    </div>

    <!-- 测评范围弹窗 -->
    <el-dialog
      v-model="assessmentRangeVisible"
      width="557px"
      :show-close="false"
      :modal="true"
      :close-on-click-modal="false"
      class="assessment-range-dialog"
    >
      <template #header>
        <div class="dialog-header">
          <el-icon class="close-icon" @click="assessmentRangeVisible = false">
            <Close />
          </el-icon>
        </div>
      </template>

      <div class="dialog-content">
        <!-- <img src="@/assets/img/entranceAssessment/csbg.png"  alt=""> -->
        <div class="title-bj">测试范围</div>
        <span class="assessment-title">{{ detailData.name }}</span>
        <span class="assessment-desc">AI根据重难点和薄弱知识点，已组好测评卷</span>

        <div class="assessment-stats">
          <div class="stat-item">
            <div class="stat-number">{{ detailData.points }}</div>
            <div class="stat-label">覆盖知识点</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ detailData.quesCount }}</div>
            <div class="stat-label">题量</div>
          </div>
          <div class="stat-item">
            <div class="stat-number-wrapper">
              <div class="time-select-wrapper">
                <span class="time-number">{{ selectedTime }}</span>
                <svg class="dropdown-arrow" :class="{ 'arrow-up': !timeDropdownVisible, 'arrow-down': timeDropdownVisible }" width="12" height="8" viewBox="0 0 12 8" fill="none">
                  <path d="M1 7L6 2L11 7" stroke="#333" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <el-select
                  v-model="selectedTime"
                  class="time-dropdown"
                  @change="handleTimeChange"
                  @visible-change="handleDropdownVisible"
                >
                  <el-option
                    v-for="time in timeOptions"
                    :key="time"
                    :label="time"
                    :value="time"
                  />
                </el-select>
              </div>
            </div>
            <div class="stat-label">预计答题时间(分)</div>
          </div>
        </div>

        <div class="dialog-actions">
          <button class="preview-btn" @click="previewQuestions">
            查看试题 >
          </button>
          <button class="start-btn" @click="startAssessmentTest" :disabled="startTestLoading">
            <span v-if="startTestLoading" class="loading-spinner"></span>
            <span v-if="!startTestLoading">开始答题</span>
            <span v-else>加载中...</span>
          </button>
        </div>
      </div>
    </el-dialog>

    <!-- 测评记录弹窗 -->
    <el-dialog
      v-model="assessmentRecordVisible"
      width="1090px"
      :show-close="false"
      :modal="true"
      :close-on-click-modal="false"
      class="assessment-record-dialog"
    >
      <template #header>
        <div class="dialog-header">
          <div class="dialog-title">
            <span class="title-text">测评记录</span>
          </div>
          <el-icon class="close-icon" @click="assessmentRecordVisible = false">
            <Close />
          </el-icon>
        </div>
      </template>

      <div class="dialog-content" v-if="assessmentRecords.length>=1">
        <el-table :data="assessmentRecords" style="width: 100%">
          <el-table-column type="index" label="序号" width="60" align="center" />
          <el-table-column prop="subject" label="科目" width="80" align="center" >
            <template #default="scope">
              <div class="score-container">
                <span>
                  {{subjectObj.subjectName}}
                </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="title" label="测评内容" min-width="200" />
          <el-table-column prop="spanTime" label="测评时间" width="350" align="center" />
          <el-table-column prop="correctRate" label="测评得分" width="120" align="center">
            <template #default="scope">
              <div class="score-container">
                <span :class="['score-text', getScoreClass(scope.row.correctRate)]">
                  {{ scope.row.correctRate % 1 === 0 ? Math.floor(scope.row.correctRate) : scope.row.correctRate }}分
                </span>
                <img class="link-img" :src="getScoreImage(scope.row.correctRate)" alt="">
              </div>
            </template>
          </el-table-column>
          <el-table-column label="详情" width="100" align="center">
            <template #default="scope">
              <span class="detail-link" @click="viewDetails(scope.row)">详情报告</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="not" style="text-align: center;" v-else>
          <img class="not-img" src="@/assets/img/entranceAssessment/nothave.png" alt="">
          <span style="display: block;">暂无测评记录</span>
      </div>
    </el-dialog>
    <div v-if="elevatePop" class="elevate-overlay">
      <div class="elevate-ct">
        <div class="close-btn" @click="elevatePop = false">×</div>
        <div class="click-bt">
          <img @click="onLearn(1)" src="@/assets/img/entranceAssessment/yztzl.png" style="width: 256px;height: 320px;margin-right: 100px;" alt=""></img>
          <img @click="onLearn(2)" src="@/assets/img/entranceAssessment/yztzl.png" style="width: 256px;height: 320px;"alt=""></img>
        </div>
        <div  @click="elevatePop = false" class="close-btn"><img src="@/assets/img/entranceAssessment/gban.png" alt="" style="width: 40px;height: 40px;"></div>
      </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Close } from '@element-plus/icons-vue'
import { storeToRefs } from 'pinia'
import subjectSelect from "@/views/components/subjectSelect/index.vue"
import { getChapterReportListApi } from "@/api/report"
import { dataEncrypt } from "@/utils/secret"

import {
  getUnitAssessmentListApi,
  getMidtermAssessmentApi,
  getFinalAssessmentApi,
  startUnitAssessmentApi,
  startMidtermAssessmentApi,
  startFinalAssessmentApi,
  getLearningModesApi,
  saveLearningModeApi,
  getAssessmentOverviewApi
} from '@/api/assessment'
import { getChapterListApi, getpointListApi, getBookChapterListApi } from "@/api/book"
import { createTrainToIntroducedApi, getDetailsApi, listApi, updateTrainTotalTimeApi } from "@/api/training"


// 预先导入所有评分图片
import asImg from '@/assets/img/entranceAssessment/as.png'
import aImg from '@/assets/img/entranceAssessment/a.png'
import bsImg from '@/assets/img/entranceAssessment/bs.png'
import bImg from '@/assets/img/entranceAssessment/b.png'
import cImg from '@/assets/img/entranceAssessment/c.png'
import { number } from 'echarts'
import { useUserStore } from "@/store/modules/user"
const userStore = useUserStore()
const chapterName = ref(userStore.chapterObj.chapterName)
const chapterType= ref(userStore.chapterObj.type)
const chapterIsName = reactive(userStore.selectedChapterInfo_synchronous.name)

const learnUsers = JSON.parse(localStorage.learnUsers || "[]") || []
const router = useRouter()

let { subjectObj, learnNow,chapterObj } = storeToRefs(userStore)
const subject = computed(() => {
  return subjectObj.value.subject
})
const wenke = [11, 12, 14, 24, 26, 27, 28, 29, 35, 36, 37, 38, 39]

const isWen = computed(()=>{
  return wenke.includes(subjectObj.value.id)
})
const bookVersionName = computed(() => {
  return subjectObj.value.editionName + learnNow.value.gradeName + (subjectObj.value.termName?subjectObj.value.termName:"")
})

// 响应式数据
const tionName = ref('')
const selectedUnit = ref('')
const selectedUnitId = ref()
const trainingId = ref()
const totalTime = ref()
const selectedMode = ref('')
const loading = ref(false)
const startTestLoading = ref(false)
const assessmentRangeVisible = ref(false)
const assessmentRecordVisible = ref(false)
const selectedTime = ref(25)
const timeOptions = [20, 25, 30, 35, 40]
const timeDropdownVisible = ref(false)
const overviewData = ref({
  totalStudents: 0,
  onlineStudents: 0,
  completedAssessments: 0
})
const detailData = ref({
  name: "",
  points: 0,
  quesCount: 0,
  trainingId: 0
})
const elevatePop = ref(false)

// 单元列表数据
const unitList = ref([
  { id: 1, name: '第一单元：名师配新名称' },
  { id: 2, name: '第二单元：名师配新名称' },
  { id: 3, name: '第三单元：名师配新名称' },
  { id: 4, name: '第四单元：名师配新名称' },
  { id: 5, name: '第五单元：名师配新名称' },
  { id: 6, name: '第六单元：名师配新名称' }
])

watch(
	chapterName,(newVlaue) => {},
	{
		deep: true
	}
);

// 单元选项下拉数据
const unitOptions = ref([
  { id: 1, name: '各种各师配新名称' },
])
const options = ref(userStore.chapterList || [])
let selectchapterId = ref([] as any[])
const pageStatus = ref(true)
const chapterId = ref(chapterObj.value.chapterId || "")
const pageData = reactive({
  total: 0,
  current: 1,
  size: 10
})
const testList = ref([] as any)
const testList2 = ref([] as any)
const setSubject = async (data: any) => {
  await getChapterList()
  await getChapterReportList()
}

//获取章节知识点树
const getChapterList = async() => {
  console.log(subjectObj.value.bookId,"打印一下 418 ")
  loading.value = true
  try {
    const res: any = await getChapterListApi({
      bookId: subjectObj.value.bookId
    })
    loading.value = false
    if (res.code == 200) {
      // 处理单元测试章节id重复无法选中和change事件
      let list = res.data || []
      if (list.length) {
        for (let i of list) {
          for (let n of i.children) {
            if (n.chapterName == '单元测试') {
              n.chapterId+= '单元测试'
            } else if(i.chapterId == n.chapterId) {
              n.chapterId+= '章节'
            }
          }
        }
      }
      options.value = list
      userStore.setChapterList(list)
      selectchapterId.value = getLastId(res.data[0]).chapterIdAll
      chapterId.value = selectchapterId.value[selectchapterId.value.length - 1]
      const firstName = getLastId(res.data[0]).chapterNameAll
      const lastchapterName = getLastId(res.data[0]).chapterNameAll
      let fullName = ''
      firstName.map(item=>{
        fullName += (item + '/')
      })
      fullName +=lastchapterName
      userStore.setChapterId(chapterId.value, fullName)
      // nextTick(() => {

      //   //重新赋值，避免在学情切换学科后获取不到数据
      //   chapterId.value = chapterObj.value.chapterId
      // })

      pageStatus.value = getLastId(res.data[0]).chapterName != "单元测试"
    }
  } catch (error) {
    loading.value = false
    console.log(error)
  }
}

//获取试卷列表
const getChapterReportList = async () => {
  getBookChapterList()
  loading.value = true
  let chapterId2 = chapterId.value
  if (chapterId2.indexOf('单元测试') > -1) {
    //单元测试
    pageStatus.value = false
    try {
      const params = {
        current: pageData.current,
        size: pageData.size,
        bookId: subjectObj.value.bookId,
        chapterId: chapterId2.replace('单元测试',''),
        type: 1
      }
      const res: any = await getChapterReportListApi(params)
      if (res.code == 200) {
        testList2.value = res.data.records || []
        pageData.total = Number(res.data.total)
      }
      loading.value = false
    } catch (error) {
      console.log(error)
      loading.value = false
    }
  } else {
    // 知识点
    pageStatus.value = true
    try {
      const res: any = await getpointListApi({
        chapterId:chapterId2.replace('章节','')
      })
      if(res.code == 200) {
        testList.value = res.data || []
      }
      loading.value = false
    } catch (error) {
      console.log(error)
      loading.value = false
    }
  }
}

const getLastId = (data: any) => {
  let lastIdAll = [] as any[]
  let lastNameAll = [] as any[]
  let lastchapterName = ""
  const getLastIds = (data1: any) => {
    if (data1.children && data1.children.length > 0) {
      lastIdAll.push(data1.chapterId)
      lastNameAll.push(data1.chapterName)
      return getLastIds(data1.children[0])
    } else {
      lastIdAll.push(data1.chapterId)
      lastchapterName = data1.chapterName
    }
  }
  getLastIds(data)
  return {chapterIdAll: lastIdAll,chapterNameAll: lastNameAll, chapterName: lastchapterName}
}


// 期中期末测评数据
const assessmentData = reactive({
  midterm: {
    available: true,
    completed: false,
    score: 0,
    description: '针对期中考试的综合性知识点进行测评'
  },
  final: {
    available: true,
    completed: false,
    score: 0,
    description: '针对整学期学习内容的综合性知识点进行测评'
  }
})

const learningNewModes = ref([
  {tips:"同步训练",description:"青铜→白银。较易题过关练习",isPrompt:false,type:1},
  {tips:"提升训练",description:"黄金→钻石。中等题/较难题过关练习",isPrompt:true,type:2},
  {tips:"拔高训练",description:"压轴题专练 ｜ 奥数题专练",isPrompt:false,type:3},
  {tips:"备考模式",description:"学段所有知识点练习，适合毕业班总复习",isPrompt:false,type:4},
])

// 学习模式数据
const learningModes:any = computed(() => {
  const allModes = [
    {
      id: 'synchronous',
      title: '同步模式',
      description: '数据家长时间关系表，五步学习法',
      color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      available: true,
      type:1
    },
    {
      id: 'improvement',
      title: '提高模式',
      description: '突破难点，提高短口',
      color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
      available: true,
      type:2
    },
    {
      id: 'examPrep',
      title: '备考模式',
      description: '掌握小考知识点关键表，五步学习法',
      color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
      available: true,
      type:3
    }
  ];
  // return allModes.filter(mode => mode.id !== 'improvement' || subjectObj.value.subjectName === '数学');

  const highSchoolGrades = ['高一', '高二', '高三'];

  if (highSchoolGrades.includes(learnNow.value.gradeName)) {
    return allModes.filter(mode => mode.id !== 'improvement');
  }

  if(subjectObj.value.subjectName !== '数学'){
      return allModes.filter(mode => mode.id !== 'improvement');
  }

  return allModes;
})

// 计算属性
const studentCountText = computed(() => {
  return `人数统计 共计${overviewData.value.totalStudents}人，在线${overviewData.value.onlineStudents}人`
})

// 测评记录数据
const assessmentRecords = ref([

])

// 初始化
onMounted(async () => {
  // 默认选择第一个单元
  selectedUnit.value = unitList.value[0]?.id.toString() || ''
  // 加载数据
  await Promise.all([
    // loadOverviewData(),
    // loadAssessmentData(),
    getBookChapterList()
  ])
})

// 获取教材下章节列表
const getBookChapterList = async() => {
    return new Promise((resolve, reject) => {
        const res: any = getBookChapterListApi({
            bookId: subjectObj.value.bookId,
            hierarchy: 1,
            type: 0
        }).then((res: any)=>{
            if(res.code == 200) {
              unitList.value = res.data || []
              tionName.value = res.data [0].name
              selectedUnit.value = unitList.value[0]?.id.toString() || ''
            }
            resolve(true)
        }).catch(()=>{
            // chapterState.options = []
            reject()
        })
    })
}


// 加载概览数据
const loadOverviewData = async () => {
  try {
    const res: any = await getAssessmentOverviewApi({
      bookId: subjectObj.value.bookId
    })
    if (res.code === 200) {
      overviewData.value = res.data || {}
    }
  } catch (error) {
    console.log('获取概览数据失败:', error)
    // 使用模拟数据
    overviewData.value = {
      totalStudents: 328,
      onlineStudents: 156,
      completedAssessments: 89
    }
  }
}

// 点击 切换按钮
const onChange = async () => {
  console.log("点击切换")

}

// 测评记录数据
const getRecordDetails = () =>{
      return new Promise((resolve, reject) => {
        const res: any = listApi({
            learnType:4,
            bookId:subjectObj.value.bookId,
            subject: subjectObj.value.subject,
            current: 1,
            size: 10,
        }).then((res: any)=>{
            if(res.code == 200) {
              assessmentRecords.value = res.data.records
              assessmentRecordVisible.value = true
            }
            resolve(true)
        }).catch(()=>{
            // chapterState.options = []
            reject()
        })
    })
}

// 测评范围数据
const getDetails = () =>{
  const cacheKey = `assessment_detail_${trainingId.value}`
  const cachedData = sessionStorage.getItem(cacheKey)
  if (cachedData) {
    const curData=JSON.parse(cachedData)?.data
    assessmentRangeVisible.value = true
    detailData.value.name= curData.title
    detailData.value.points = curData.pointIds.length
    detailData.value.quesCount = curData.quesCount
    detailData.value.trainingId = curData.trainingId
    loading.value = false
    return true
  }
      return new Promise((resolve, reject) => {
        const res: any = getDetailsApi({
            trainingId:trainingId.value
        }).then((res: any)=>{
            if(res.code == 200) {
              assessmentRangeVisible.value = true
              detailData.value.name= res.data.title
              detailData.value.points = res.data.pointIds.length
              detailData.value.quesCount = res.data.quesCount
              detailData.value.trainingId = res.data.trainingId

              // 存储试卷详情到sessionStorage
              const cacheData = {
                trainingId: res.data.trainingId,
                data: res.data,
                timestamp: Date.now()
              }
              sessionStorage.setItem(cacheKey, JSON.stringify(cacheData))
              loading.value = false
            }
            resolve(true)
        }).catch(()=>{
            // chapterState.options = []
            reject()
        })
    })
}
// 加载测评数据
const loadAssessmentData = async () => {
    return new Promise((resolve, reject) => {
        const res: any = createTrainToIntroducedApi({
            bookId:subjectObj.value.bookId,
            chapterId: selectedUnit.value,
            noteSource:selectedUnitId.value
        }).then((res: any)=>{
            if(res.code == 200) {
              trainingId.value=res.data
              getDetails()

            }
            resolve(true)
        }).catch(()=>{
            // chapterState.options = []
            reject()
            loading.value = false
        })
    })

}
// 开始单元测评
const startUnitAssessment = async () => {
  if (!selectedUnit.value) {
    ElMessage.warning('请先选择单元')
    return
  }

  loading.value = true
  try {
    const res: any = await startUnitAssessmentApi({
      unitId: selectedUnit.value,
      bookId: subjectObj.value.bookId
    })

    if (res.code === 200) {
      ElMessage.success('开始单元测评')
      // 跳转到测评页面
      router.push({
        path: '/ai_percision/assessment_test',
        query: {
          type: 'unit',
          unitId: selectedUnit.value,
          testId: res.data.testId
        }
      })
    } else {
      ElMessage.error(res.message || '开始测评失败')
    }
  } catch (error) {
    console.log('开始单元测评失败:', error)
    ElMessage.error('开始测评失败')
  } finally {
    loading.value = false
  }
}

// 开始期中测评
const startMidtermAssessment = async () => {
  if (!assessmentData.midterm.available) {
    ElMessage.warning('期中测评暂不可用')
    return
  }

  loading.value = true
  try {
    const res: any = await startMidtermAssessmentApi({
      bookId: subjectObj.value.bookId
    })

    if (res.code === 200) {
      ElMessage.success('开始期中测评')
      router.push({
        path: '/ai_percision/assessment_test',
        query: {
          type: 'midterm',
          testId: res.data.testId
        }
      })
    } else {
      ElMessage.error(res.message || '开始测评失败')
    }
  } catch (error) {
    console.log('开始期中测评失败:', error)
    ElMessage.error('开始测评失败')
  } finally {
    loading.value = false
  }
}

// 开始期末测评
const startFinalAssessment = async () => {
  if (!assessmentData.final.available) {
    ElMessage.warning('期末测评暂不可用')
    return
  }

  loading.value = true
  try {
    const res: any = await startFinalAssessmentApi({
      bookId: subjectObj.value.bookId
    })

    if (res.code === 200) {
      ElMessage.success('开始期末测评')
      router.push({
        path: '/ai_percision/assessment_test',
        query: {
          type: 'final',
          testId: res.data.testId
        }
      })
    } else {
      ElMessage.error(res.message || '开始测评失败')
    }
  } catch (error) {
    console.log('开始期末测评失败:', error)
    ElMessage.error('开始测评失败')
  } finally {
    loading.value = false
  }
}

const isMode =ref({
  id:1,
  type:1
})

const onLearn = async (mode: any) => {
  elevatePop.value = false
  router.push({
    path: '/ai_percision/final_question',
  })
}
// 选择学习模式
const selectMode = async (mode: any) => {

  console.log(mode,"entrance_assessmententrance_assessmententrance_assessment")
  if(mode.type=='1'||mode.type=='2'){
    router.push({
      path: '/ai_percision/basic_training',
      query: {
          bookId:subjectObj.value.bookId,
          type:mode.type,
          subject: subjectObj.value.id,
        }
    })
  }
  if(mode.type=='3'){
    elevatePop.value = true
    return
  }
  if(mode.type=='4'){
    router.push({
      path: '/ai_percision/knowledge_graph_detail',
      query: {
          type:mode.type,
          subject: subjectObj.value.id,
          dataType:mode.type
        }
    })
  }
  // const previousMode = selectedMode.value

  // selectedMode.value = mode.id

  // const modeData = learningModes.value.find(m => m.id === mode.id)

  //   router.push({
  //   path: '/ai_percision/basic_training',
  //   query: {
  //       type:mode.id,
  //       subject: subjectObj.value.id,
  //       dataType:mode.type
  //     }
  // })

  // if (!modeData?.available) {
  //   ElMessage.warning('该学习模式暂不可用')
  //   selectedMode.value = previousMode
  //   return
  // }

  // try {
  // } catch (error) {
  //   console.log('保存学习模式失败:', error)
  //   selectedMode.value = previousMode
  // }
}

// 继续学习
const continueStudy = async () => {
  let modeData:any = learningModes.value.find(m => m.title === chapterType.value)
    router.push({
    path: '/ai_percision/knowledge_graph_detail',
    query: {
        type:modeData.id,
        subject: subjectObj.value.id,
        dataType:modeData.type
      }
  })
}

// 时间选择变化处理
const handleTimeChange = async(value: number) => {
    try {
    const res: any = await updateTrainTotalTimeApi({
      trainingId: detailData.value.trainingId,
      totalTime: value
    })
    console.log(res,"修改时间成功")
  } catch (error) {
    ElMessage.error('修改时间失败')
  }

}

// 处理下拉框显示状态
const handleDropdownVisible = (visible: boolean) => {
  timeDropdownVisible.value = visible
}

// 查看试题
const previewQuestions = () => {

      router.push({
      path: '/ai_percision/entrance_assessment/test_questions',
      query: {
        data: dataEncrypt({
          reportId: detailData.value.trainingId,
          pageSource: '1'
        }),
      }
    })
}

// 开始测评测试
const startAssessmentTest = async () => {
  try {
    startTestLoading.value = true

    // 模拟加载延迟
    await new Promise(resolve => setTimeout(resolve, 2000))

    assessmentRangeVisible.value = false

    // ElMessage.success('开始测评')

    // 跳转到测评页面
    router.push({
      path: '/ai_percision/entrance_assessment/answer_questions',
      // path: '/ai_percision/entrance_assessment/doing_exercises',
      query: {
        data: dataEncrypt({
          reportId: detailData.value.trainingId,
          pageSource: '1'
        }),
      }
    })
  } catch (error) {
    console.error('开始测评失败:', error)
    ElMessage.error('开始测评失败，请重试')
  } finally {
    startTestLoading.value = false
  }
}

// 显示测评范围弹窗 (供其他地方调用)
const showAssessmentRange = (val:number) => {
  loading.value = true

  selectedUnitId.value = val

  loadAssessmentData()

  // assessmentRangeVisible.value = true

}

// 显示测评记录弹窗
const showAssessmentRecord = () => {
  getRecordDetails()
}

// 获取分数样式类
const getScoreClass = (score: number) => {
  if (score >= 90) return 'score-excellent'
  if (score >= 80) return 'score-good'
  if (score >= 70) return 'score-average'
  if (score >= 60) return 'score-below'
  return 'score-poor'
}

// 根据分数获取对应的图片
const getScoreImage = (score: number) => {
  if (score >= 90) return asImg    // A+ 图片
  if (score >= 80) return aImg     // A 图片
  if (score >= 70) return bsImg    // B+ 图片
  if (score >= 60) return bImg     // B 图片
  return cImg                      // C 图片（60分以下）
}

// 获取等级样式类
const getGradeClass = (grade: string) => {
  switch (grade) {
    case 'A+':
      return 'grade-a-plus'
    case 'A':
      return 'grade-a'
    case 'B+':
      return 'grade-b-plus'
    case 'B':
      return 'grade-b'
    case 'C+':
      return 'grade-c-plus'
    case 'C':
      return 'grade-c'
    default:
      return 'grade-default'
  }
}

// 查看详情
const viewDetails = (record: any) => {

  // ElMessage.success(`查看${record.subject} - ${record.content}的详细信息`)
  // 这里可以跳转到详情页面或显示详情弹窗

  // getDetails()
    router.push({
      path: '/ai_percision/entrance_assessment/test_report',
      query: {
        data: dataEncrypt({
          reportId: record.trainingId,
          pageSource: '1'
        }),
      }
    })
}

// 跳转到测试报告页面
const goToTestReport = () => {
  router.push({
    path: '/ai_percision/entrance_assessment/test_report',
    query: {
      pointId: selectedUnit.value , // 这里应该是实际的测试ID
      subject: subjectObj.value.bookId
    }
  })
}

const onModify = () =>{
  console.log(learnUsers[0].learnId,"learnUserslearnUserslearnUsers")
    router.push({
    path: '/user/user_add',
    query: {
      learnId: learnUsers[0].learnId,
      pageType:'edit'
    }
  })
}

</script>

<style lang="scss" scoped>
.graph-content {
  display: flex;
}
.entrance-assessment-container {

  width: 70rem;
  background: #f5f7fa;
  min-height: 100vh;
  margin-left: .625rem;
  .header-banner {
    margin-bottom: 30px;
    background: #fff;
    border-radius: 20px 20px 0 0;
    padding-bottom: 1rem;
    box-shadow: 0px 6px 16px 0px rgba(185, 194, 200, 0.3);
    .header-title{
        background: url('@/assets/img/entranceAssessment/newTopbj.png')no-repeat;background-size: 100%;
        
        padding: 2rem 25px;
        display: flex;
        font-weight: 600;
        font-size: 16px;
        .exchange{
          width: 24px;
          height: 24px;
          margin-right: 8px;
        }

    .title-box{
        font-weight: 600;
    }
    .switch{
      display: flex;
      width: 92px;
      height: 27px;
      line-height: 27px;
      background: #b0b4ba;
      border-radius: 22px;
      color: #fff;
      font-size: 14px;
      // justify-content: center;
      align-items: center;
      margin-left: 10px;
      font-weight: 400;
      cursor: pointer;
      img{
        width: 12px;
        height: 11px;
        margin-left: 10px;
        margin-right: 2px;
      }
    }
    .banner-tipss{
      margin-left: auto;
      font-size: 14px;
      font-weight: 300;
    }
}
.pattern{
  display: flex;
  .pattern-list{
    width: 343px;
    height: 230px;
    margin-left: 22px;
    margin-bottom: 12px;
    position: relative;
    cursor: pointer;
    &:hover {
      // transform: translateY(-2px);
    }
    &:nth-child(1) {
      background: url('@/assets/img/entranceAssessment/jcxl.png')no-repeat; background-size: 100%;
      
      .start-learn {
        color: rgba(90, 133, 236, 1);
      }
    }
    
    &:nth-child(2) {
      background: url('@/assets/img/entranceAssessment/tsxl.png')no-repeat; background-size: 100%;
      
      .start-learn {
        color: rgba(149, 90, 236, 1);
      }
    }
    
    &:nth-child(3) {
      background: url('@/assets/img/entranceAssessment/tgxl.png')no-repeat; background-size: 100%;
      
      .start-learn {
        color: rgba(90, 133, 236, 1);
      }
    }
    
    &:nth-child(4) {
      background: url('@/assets/img/entranceAssessment/bkmss.png')no-repeat; background-size: 100%;
      
      .start-learn {
        color: rgba(149, 90, 236, 1);
      }
    }
    .top-tps{
      width: 92px;
      height: 37px;
      font-size: 16px ;
      font-weight: 700;
      border-radius: 20px 0 20px 0 ;
      color: #fff;
      background: rgba(0, 0, 0, 0.4);
      text-align: center;
      line-height: 37px;
    }
    .learn{
      padding: 0 10px;
      padding-top: 120px;
      overflow: hidden;
      span{
        font-size: 14px;
        display: block;
        color: rgba(42, 43, 42, 1);
      }
      .start-learn{
        float: right;
        width: 108px;
        line-height: 37px;
        background: rgba(255, 255, 255, 1);
        text-align: center;
        font-size: 16px;
        border-radius: 28px;
      }
    }
    .prompt{
      background: url("@/assets/img/entranceAssessment/prompt.png") no-repeat;
      background-size: 100%;
      position: absolute;
      bottom: -96px;
      right: -200px;
      z-index: 999;
      width: 402px ;
      height: 113px;
      overflow: hidden;
      .prompt-top{
        display: flex;
        padding-top: 16px;
        padding-left: 24px;
        height: 26px;
        img{
          width: 23px;
          height: 23px;
        }
        span{
          color: rgba(0, 201, 163, 1);
          font-size: 14px;
          font-weight: 700;
          padding-left: 4px;
        }
        .rig-cha{
          width: 30px;
          height: 30px;
          margin-left: auto;
          img{
            width: 10px;
            height: 10px;
          }
        }
      }
      .prompt-title{
        color: #fff;
        font-size: 14px;
        padding-top: 0;
        padding-left: 50px;
        width: 330px; /* 固定宽度 */
        white-space: nowrap; /* 禁止换行 */
        overflow: hidden; /* 隐藏溢出的内容 */
        text-overflow: ellipsis; /* 显示省略号 */
      }
      .continue{
        float: right;
        font-size: 14px;
        font-weight: 700;
        color: #fff;
        text-decoration: underline;
        margin-right: 20px;
        padding-top: 6px;
        &:hover {
          transform: translateY(-2px);
          color: #44A08D;
        }
      }
    }
  }
}

    .banner-content {
      background: url("@/assets/img/entranceAssessment/back.png") no-repeat;
      background-size: 100%;
      border-radius: 16px;
      padding: 28px 40px;
      color: white;
      position: relative;
      min-height: 120px;
      margin: 0 1rem;

      .banner-tips{
        position: relative;
        color: rgb(0, 111, 90);
        font-size: 14px;
        letter-spacing: 0px;
        text-align: left;
      }

      .banner-left {
        flex: 1;
        display: flex;
        align-items: center;
        gap:10px;
        position: relative;
        z-index: 1;
        .isp{
          text-decoration: underline !important;
          cursor: pointer;
          padding-left: 10px;
        }
        .banner-icon {
          width: 60px;
          height: 60px;
          background: rgba(255, 255, 255, 0.2);
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          backdrop-filter: blur(10px);
          .rmImg{
            width: 28px;
            height: 32px;
          }
          i {
            font-size: 32px;
            color: white;
          }
        }

        .banner-text {
          .banner-title {
            margin: 0 16px 0 0;
            font-size: 28px;
            font-weight: 600;
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

          }

          .banner-subtitle {
            margin: 0;
            font-size: 16px;
            font-weight: 400;
            margin-left: 20px;
            color: rgba(255, 255, 255, 0.9);
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover {
              color: white;
              transform: translateX(4px);
            }
          }
        }
      }

      .banner-right {
        position: absolute;
        top:-3rem;
        right: 0;
        z-index: 1;
        width: 240px;
        height: 239px;

        .character-image {
            width: 240px;
            height: 239px;
        }
      }
    }
  }

  .assessment-selection {
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 24px;
    margin-top: -3rem;
    padding: 0 1rem;
    .assessment-section {
        border-radius: 20px 20px 0 0 ;
        backdrop-filter: blur(50px);
        background: rgba(255, 255, 255, 0.8);
        border-radius: 12px;
        padding: 24px;
        transition: transform 0.2s ease;
        box-shadow: 0px 6px 16px 0px rgba(185, 194, 200, 0.3);

      .section-title {
        margin: 0 0 16px 0;
        font-size: 18px;
        font-weight: 600;
        color: #333;
        text-align: center;
      }

      .section-desc {
        margin: 0 0 20px 0;
        color: #666;
        font-size: 14px;
        line-height: 1.5;
      }

      .unit-selector {
        .unit-select {
          width: 100%;
          margin-bottom: 16px;

          :deep(.el-input__inner) {
            border-radius: 8px;
            border: 1px solid #e0e0e0;

            &:focus {
              border-color: #4ECDC4;
            }
          }
        }

        .dropdown-options {
          .dropdown-option {
            padding: 8px 12px;
            font-size: 14px;
            color: #666;
            cursor: pointer;
            border-radius: 6px;
            transition: background-color 0.2s ease;

            &:hover {
              background: #f0f9ff;
              color: #4ECDC4;
            }
          }
        }
      }

      .assessment-btn {
        width: 122px;
        height: 38px;
        border-radius: 18px;
        background: rgb(0, 201, 163);
        border: none;
        font-size: 14px;
        margin: 0 auto;
        line-height: 38px;
        display: flex;
        &:hover {
          background: #44A08D;
        }
      }
      .two-bt{
        margin-top:40px
      }
    }
  }

  .learning-modes {
    padding:  30px 50px;
    background: #fff;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
    .top-nav{
      display: flex;
      img{
        width: 133px;
        height: 57px;
      }
      .test{
        color: rgba(190, 90, 24, 1);
        font-size: 16px;
        font-weight: 700;
        padding-top: 22px;
        padding-left: 16px;
      } 
      .banner-subtitle {
        margin: 0;
        font-size: 16px;
        font-weight: 400;
        margin-left: 40px;
        color: rgba(0, 156, 127, 1);
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: underline;
        padding-top: 22px;
        &:hover {
          color: #333;
          transform: translateX(2px);
        }
      }
      .rig-introduction{
        margin-left: auto;
        padding-top: 20px;
        color: rgba(153, 153, 153, 1);
        font-size: 14px;
      }
    }
    .measure-ct{
      width: 100%;
      height: 161px;
      background: #f5f5f5;
      border-radius: 20px;
      
      display: flex;
      .measure-list{
        width: 32%;
        text-align: center;
        .measure-tt{
          text-align: center;
          padding: 20px 0 10px 0;
          color: rgba(42, 43, 42, 1);
          font-size: 16px;
          font-weight: 700;
        }
        span{
          display: block;
          font-size: 14px;
          font-weight: 400;
          color: rgba(102, 102, 102, 1);
          padding-bottom: 10px;
        }
        .measure-btn{
          border: none;
          color: #333 !important;
          width: 144px;
          line-height: 37px;
          height: 37px;
          background: rgba(221, 221, 221, 1);
          border-radius: 28px;
          margin: 20px auto;
          &:hover {
          background: #44A08D;
        }
        }
      }
    }
    .modes-title {
      font-size: 16px;
      color: #666;
      margin-bottom: 20px;
      font-weight: 400;
    }

    .modes-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 24px;

      .mode-card {
        background: white;
        border-radius: 20px;
        padding: 24px 20px;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
        position: relative;
        overflow: hidden;
        min-height: 200px;

        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        }

        &.selected {
          border: 3px solid #4ECDC4;
          box-shadow: 0 8px 24px rgba(78, 205, 196, 0.3);
        }

        &.disabled {
          opacity: 0.5;
          cursor: not-allowed;

          &:hover {
            transform: none;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
          }
        }

        .mode-status {
          position: absolute;
          top: 6px;
          right: 6px;
          color: #333;
          z-index: 2;

          img{
            width: 110px;
            height: 32px;
          }
        }

        .selected-indicator {
          position: absolute;
          top: 16px;
          left: 16px;
          width: 24px;
          height: 24px;
          background: #4ECDC4;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-weight: bold;
          font-size: 14px;
          z-index: 2;
        }

        .mode-icon {
          height: 80px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 20px;
          position: relative;
        }

        .mode-title {
          margin: 0 0 12px 0;
          font-size: 20px;
          font-weight: 600;
          color: white;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .mode-desc {
          margin: 0;
          font-size: 14px;
          color: rgba(255, 255, 255, 0.9);
          line-height: 1.4;
        }

        // 同步模式样式
        &.synchronous-mode {
          background:  url("@/assets/img/entranceAssessment/tb.png") no-repeat;
          background-size: 100%;
          .sync-circles {
            position: relative;

            .circle {
              width: 40px;
              height: 40px;
              border: 3px solid rgba(255, 255, 255, 0.6);
              border-radius: 50%;
              position: absolute;

              &.circle-1 {
                top: 0;
                left: 0;
                animation: pulse 2s infinite;
              }

              &.circle-2 {
                top: 10px;
                left: 20px;
                animation: pulse 2s infinite 0.5s;
              }
            }
          }

          // 添加球体装饰
          &::before {
            content: '';
            position: absolute;
            top: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%);
            border-radius: 50%;
            z-index: 0;
          }

          &::after {
            content: '';
            position: absolute;
            bottom: 20px;
            left: 20px;
            width: 30px;
            height: 30px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            z-index: 0;
          }
        }

        // 提高模式样式
        &.improvement-mode {
          background:  url("@/assets/img/entranceAssessment/tgms.png") no-repeat;
          background-size: 100%;
          .improvement-shapes {
            position: relative;

            .cube {
              width: 30px;
              height: 30px;
              background: rgba(255, 255, 255, 0.8);
              transform: rotate(45deg);
              position: absolute;
              top: 10px;
              left: 10px;
              animation: rotate 3s linear infinite;
              border-radius: 4px;
            }

            .cube-shadow {
              width: 24px;
              height: 24px;
              background: rgba(255, 255, 255, 0.6);
              transform: rotate(45deg);
              position: absolute;
              top: 20px;
              left: 40px;
              animation: rotate 3s linear infinite 1s;
              border-radius: 4px;
            }
          }

          // 添加立方体装饰
          &::before {
            content: '';
            position: absolute;
            top: 15px;
            right: 15px;
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            transform: rotate(15deg);
            border-radius: 6px;
            z-index: 0;
          }
        }

        // 备考模式样式
        &.examPrep-mode {
          background:  url("@/assets/img/entranceAssessment/bkms.png") no-repeat;
          background-size: 100%;
          .exam-shapes {
            position: relative;

            .polygon {
              width: 32px;
              height: 32px;
              background: rgba(255, 255, 255, 0.8);
              clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
              position: absolute;
              top: 5px;
              left: 15px;
              animation: float 3s ease-in-out infinite;
            }

            .triangle {
              width: 0;
              height: 0;
              border-left: 12px solid transparent;
              border-right: 12px solid transparent;
              border-bottom: 20px solid rgba(255, 255, 255, 0.6);
              position: absolute;
              top: 25px;
              left: 35px;
              animation: float 3s ease-in-out infinite 1s;
            }
          }

          // 添加几何装饰
          &::before {
            content: '';
            position: absolute;
            top: 20px;
            right: 25px;
            width: 0;
            height: 0;
            border-left: 25px solid transparent;
            border-right: 25px solid transparent;
            border-bottom: 30px solid rgba(255, 255, 255, 0.2);
            z-index: 0;
          }

          &::after {
            content: '';
            position: absolute;
            bottom: 30px;
            right: 30px;
            width: 20px;
            height: 20px;
            background: rgba(255, 255, 255, 0.15);
            clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
            z-index: 0;
          }
        }
      }
    }
  }

  .footer-section {
    text-align: left;
    padding: 20px 1rem 40px;
    .learning-status-bar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      background: #f8fafb;
      border-radius: 12px;
      padding-left: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
      border: 1px solid #EAEAEA;
      margin: 0;
      line-height: 33px;

      .status-content {
        display: flex;
        align-items: center;
        gap: 8px;
        flex: 1;

        .status-text {
          font-size: 14px;
          color: #333;
          font-weight: 400;
        }

        .status-mode {
          font-size: 14px;
          color: #4AB3FA;
          font-weight: 500;
        }

        .status-path {
          font-size: 14px;
          color: #666;
          font-weight: 400;
        }
      }

      .continue-btn {
        background: #4AB3FA;
        border: none;
        padding: 8px 24px;
        font-size: 14px;
        border-radius: 20px;
        color: white;
        font-weight: 500;
        box-shadow: 0 2px 8px rgba(78, 205, 196, 0.3);
        transition: all 0.3s ease;
        margin-left: 16px;

        &:hover {
          background: linear-gradient(135deg, #44A08D 0%, #4ECDC4 100%);
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(78, 205, 196, 0.4);
        }
      }
    }
  }
}

// 动画效果
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
}

@keyframes rotate {
  0% {
    transform: rotate(45deg);
  }
  100% {
    transform: rotate(405deg);
  }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-8px);
  }
}

@keyframes glow {
  0% {
    opacity: 0.3;
    transform: scale(1);
  }
  100% {
    opacity: 0.6;
    transform: scale(1.1);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .entrance-assessment-container {
    .header-banner .banner-content {
      flex-direction: column;
      text-align: center;
      padding: 24px 20px;
      gap: 20px;
      height: 386px;
      .banner-left {
        margin-right: 0;
        margin-bottom: 0;
        flex-direction: column;
        gap: 16px;

        .banner-text {
          .banner-title {
            font-size: 24px;
          }

          .banner-subtitle {
            font-size: 14px;
            margin-left: 20px;
          }
        }
      }

      .banner-right {
        position: relative;
        top: 0;
        width: 80px;
        height: 80px;

        .character-image {
          width: 80px;
          height: 80px;
        }
      }
    }

    .assessment-selection {
      grid-template-columns: 1fr;
      margin-top: 0;
    }

    .learning-modes {
      .modes-grid {
        grid-template-columns: 1fr;
        gap: 16px;

        .mode-card {
          min-height: 160px;
          padding: 20px 16px;

          .mode-title {
            font-size: 18px;
          }

          .mode-desc {
            font-size: 13px;
          }
        }
      }
    }

    .footer-section {
      text-align: center;

      .learning-status-bar {
        flex-direction: column;
        gap: 12px;

        .status-content {
          flex-direction: column;
          align-items: flex-start;
          gap: 8px;

          .status-text,
          .status-mode,
          .status-path {
            font-size: 13px;
          }
        }

        .continue-btn {
          width: 100%;
          max-width: 200px;
          margin-left: 0;
        }
      }
    }
  }
}
</style>

<!-- 测评范围弹窗样式 -->
<style lang="scss">
.assessment-range-dialog {
  background:  url("@/assets/img/entranceAssessment/fwbj.png") no-repeat;
  background-size: 100%;
  height: 371px;
  .el-dialog {
    border-radius: 20px;
    overflow: hidden;
    // box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    border: none;
    // background: linear-gradient(135deg, #ff6b6b 0%, #ff4757 100%);
    position: relative;
  }

  .el-dialog__header {
    padding: 0;
    margin: 0;
    background: transparent;
    border-bottom: none;
  }

  .el-dialog__body {
    padding: 0;
    // background: transparent;
  }

  .dialog-header {
    display: flex;
    align-items: center;
    padding: 20px 24px;

    .dialog-title {
      .title-badge {
        color: #ff4757;
        padding: 8px 20px;
        border-radius: 24px;
        font-size: 14px;
        font-weight: 600;
        backdrop-filter: blur(10px);
      }
    }

    .close-icon {
      position: absolute;
      right: 16px;
      top: 16px;
      z-index: 1000;
      font-size: 24px;
      color: #000;
      cursor: pointer;
      transition: color 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 36px;
      height: 36px;
      border-radius: 50%;
      backdrop-filter: blur(4px);

      &:hover {
        color: #333;
        background: rgba(255, 255, 255, 1);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      }
    }
  }

  .dialog-content {
    padding:  20px;
    position: relative;
    .title-bj{
      background:  url("@/assets/img/entranceAssessment/csbg.png") no-repeat;
      background-size: 100%;
      width: 117px;
      height: 39px;
      font-size: 16px;
      font-weight: 700;
      color: #fff;
      line-height: 35px;
      text-align: center;
      position: absolute;
      top: -10px;
      left: 20px;
    }
    .assessment-title {
      color: rgb(42, 43, 42);
      font-size: 20px;
      font-weight: 700;
      letter-spacing: 0px;
      text-align: left;
      display: block;
      padding-top: 10px;
    }

    .assessment-desc {
      color: rgb(102, 102, 102);
      font-size: 12px;
      font-weight: 400;
      text-align: left;
      padding-top: 10px;
    }

    .assessment-stats {
      display: flex;
      justify-content: space-between;
      margin-bottom: 40px;
      padding: 36px 20px 0 20px;

      .stat-item {
        text-align: center;
        flex: 1;

        .stat-number {
          font-size: 50px;
          font-weight: 700;
          color: #000;
          margin-bottom: 12px;
          line-height: 1;
          font-family: 'Arial', 'Microsoft YaHei', sans-serif;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .stat-number-wrapper {
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 12px;

          .time-select-wrapper {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;

            .time-number {
              font-size: 50px;
              font-weight: bold;
              color: #000;
              line-height: 1;
              margin-right: 8px;
              font-family: 'Arial', 'Microsoft YaHei', sans-serif;
            }

            .dropdown-arrow {
              margin-left: 4px;
              transition: transform 0.3s ease;

              &.arrow-up {
                // transform: rotate(0deg);
                transform: rotate(180deg);
              }

              &.arrow-down {
                 transform: rotate(0deg);
                // transform: rotate(180deg);
              }
            }

            &:hover .dropdown-arrow {
              // 移除原有的hover效果，使用状态控制
            }

            .time-dropdown {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              opacity: 0;
              cursor: pointer;

              .el-input {
                opacity: 0;

                .el-input__wrapper {
                  background: transparent;
                  box-shadow: none;
                  border: none;

                  .el-input__inner {
                    background: transparent;
                    border: none;
                    padding: 0;
                    height: 60px;
                    font-size: 50px;
                    font-weight: bold;
                    color: #1a1a1a;
                    text-align: center;
                  }
                }
              }
            }
          }
        }

        .stat-label {
          font-size: 14px;
          color: #000;
        }
      }
    }

    .dialog-actions {
      display: flex;
      gap: 32px;
      justify-content: center;
      align-items: center;

      .preview-btn {
        background: none;
        border: none;
        color: #000(255, 255, 255, 0.9);
        font-size: 16px;
        cursor: pointer;
        padding: 12px 0;
        transition: all 0.3s ease;
        text-decoration: underline;

        &:hover {
          color: #333;
          transform: translateX(2px);
        }
      }

      .start-btn {
        background:  url("@/assets/img/entranceAssessment/csbt.png") no-repeat;
        background-size: 100%;
        border: none;
        padding: 14px 56px;
        font-size: 16px;
        border-radius: 28px;
        color: #fff;
        font-weight: 600;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        transition: all 0.3s ease;

        &:disabled {
          opacity: 0.7;
          cursor: not-allowed;
          transform: none !important;
        }

        &:hover:not(:disabled) {
          box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
          transform: translateY(-1px);
        }

        &:active:not(:disabled) {
          transform: translateY(0);
        }

        .loading-spinner {
          width: 16px;
          height: 16px;
          border: 2px solid rgba(255, 255, 255, 0.3);
          border-top: 2px solid #fff;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }
      }
    }
  }
  .not{
    display: flex;
    span{
      display: block;
      color: #999999;
      font-size: 14px;
    }
  }
}

// 下拉选择器的样式优化
.el-select-dropdown__item {
  text-align: center;
  font-size: 16px;
  font-weight: 500;
}
</style>

<!-- 测评记录弹窗样式 -->
<style lang="scss">
.assessment-record-dialog {
  .el-dialog {
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    position: relative;
  }

  .el-dialog__header {
    padding-bottom: 15px;
    margin: 0;
    border-bottom: 1px solid #f0f0f0;
  }

  .el-dialog__body {
    padding: 0;
  }

  .dialog-header {
    display: flex;
    align-items: center;
    padding: 20px 24px;

    .dialog-title {
      .title-text {
        font-size: 18px;
        font-weight: 600;
        color: #333;
      }
    }

    .close-icon {
      position: absolute;
      right: 16px;
      top: 16px;
      z-index: 1000;
      font-size: 24px;
      color: #999;
      cursor: pointer;
      transition: color 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.8);
      backdrop-filter: blur(4px);

      &:hover {
        color: #333;
        background: rgba(255, 255, 255, 1);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      }
    }
  }

  .dialog-content {
    padding: 20px 24px;

    .el-table {
      border: 1px solid #f0f0f0;
      border-radius: 8px;
      overflow: hidden;

      .el-table__header {
        background: #fafafa;

        th {
          background: #fafafa !important;
          color: #333;
          font-weight: 600;
          border-bottom: 1px solid #e0e0e0;
        }
      }

      .el-table__body {
        tr {
          &:hover {
            background: #f9f9f9;
          }

          td {
            border-bottom: 1px solid #f0f0f0;
            padding: 12px 8px;
          }
        }
      }
    }

    .score-container {
      display: flex;
      // align-items: center;
      // justify-content: center;
      // gap: 8px;
      .link-img{
        width: 72px;
        height: 33px;
      }

      .grade-text {
        font-size: 12px;
        font-weight: 600;
        padding: 2px 6px;
        border-radius: 4px;
        color: white;

        &.grade-a-plus {
          background: #52c41a;
        }

        &.grade-a {
          background: #1890ff;
        }

        &.grade-b-plus {
          background: #fa8c16;
        }

        &.grade-b {
          background: #fa8c16;
        }

        &.grade-c-plus {
          background: #f5222d;
        }

        &.grade-c {
          background: #f5222d;
        }

        &.grade-default {
          background: #999;
        }
      }
    }

    .detail-link {
      color: #009C7F;
      // text-decoration: none;
      text-decoration: underline !important;
      cursor: pointer;
      &:hover {
        color: #40a9ff;
        text-decoration: underline;
      }
    }
  }
}
</style>

<style lang="scss" scoped>
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.elevate-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.elevate-ct {
  width: 620px;
  height: 500px;
  border-radius: 20px;
  padding: 30px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
}
.click-bt{
 display: flex;
 cursor: pointer;
}

.close-btn {
  margin-top: 20px;
  border-radius: 50%;
  // background-color: rgba(0, 0, 0, 0.1);
  color: #333;
  font-size: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.close-btn:hover {
  // background-color: rgba(0, 0, 0, 0.2);
  transform: scale(1.1);
}
</style>
