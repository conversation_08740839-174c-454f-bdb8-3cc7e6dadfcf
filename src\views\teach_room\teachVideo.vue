<!-- 名师课堂-知识点视频-章节进入 -->
<template>
  <div class="content" id="content">
    <div class="inner">
      <div class="wrap">
        <div class="menu_lt">
          <div class="wk_box">
            <div class="wk_tit">
              <div class="wk_p" v-html="ReplaceMathString(state.vidInfo.videoName3||state.vidInfo.videoName)"></div>
              <template v-if="state.vidType==1">
                <div class="wk_status status2" v-if="state.vidInfo.studyStatus==2">已学完</div>
                <div class="wk_status status1" v-else-if="state.vidInfo.studyStatus==1">未学完</div>
                <div class="wk_status status0" v-else>未学习</div>
              </template>
            </div>
            <!-- 播放器 -->
            <div id="dplayer1" class="wk_video" v-show="state.videoUrl|| state.videoList.length"></div>
            <div class="wk_nodata" v-show="!(state.videoUrl|| state.videoList.length)&&state.isShow">
              <img src="@/assets/img/teachroom/nopoint.png" />暂无知识点视频
            </div>
            <div class="wk_opt" v-show="state.videoUrl">
              <div class="wk_collect" :class="state.vidInfo.userCollect?'active':''" @click="setCollect">
                <img src="@/assets/img/teachroom/collect.svg" />
                <img src="@/assets/img/teachroom/collectsel.svg" />
              </div>
              <div class="wk_thumbs" :class="state.vidInfo.userLike?'active':''" @click="setThumbs">
                <img src="@/assets/img/teachroom/thumbs.svg" />
                <img src="@/assets/img/teachroom/thumbs2sel.svg" />
                {{state.vidInfo.likeNum||0}}
              </div>
            </div>
          </div>
          <template v-if="state.vidInfo.summary">
            <!-- 优学派h5 -->
            <iframe :src="state.vidInfo.summary" class="iframe" v-if="state.vidInfo.isHttp"></iframe>
            <!-- 学大html -->
            <div class="wkinfo" v-html="ReplaceMathString(state.vidInfo.summary)" v-else></div>
          </template>
        </div>
        <div class="menu_rt ">
          <div class="now_box">
            <div class="now_air">
              <img src="@/assets/img/teachroom/playing2.gif" />正在播放
            </div>
            <div class="now_img">
              <img :src="state.vidInfo.cover" style="transform: scale(1.1);" v-if="state.vidInfo.cover" />
              <img src="@/assets/img/teachroom/novid.png" v-else />
              <template v-if="state.vidType==1">
                <div class="now_status status2" v-if="state.vidInfo.studyStatus==2">已学完</div>
                <div class="now_status status1" v-else-if="state.vidInfo.studyStatus==1">未学完</div>
                <div class="now_status status0" v-else>未学习</div>
              </template>
            </div>
            <div class="now_name" v-html="ReplaceMathString(state.vidInfo.videoName3||state.vidInfo.videoName)"></div>
          </div>
          <div class="vid_h2 nowrap" v-if="state.data.length">{{state.data[0].name}}</div>
          <div class="vid_ul">
            <template v-for="(item,index) in state.data" :key="index">
              <template v-for="(item2,index2) in item.knowledgeList" :key="index2">
                <!-- 带概述 -->
                <div class="vid_li " v-for="(item3,index3) in item2.videoInfos" @click="wekePlay" :data-i="index"
                  :data-i2="index2" :data-i3="index3">
                  <div class="vid_img">
                    <img :src="item3.cover" class="wk_img" v-if="item3.cover" />
                    <img src="@/assets/img/teachroom/novid.png" class="wk_img" v-else />
                  </div>
                  <div class="vid_rt">
                    <div class="vid_name nowrap2" v-html="ReplaceMathString(item3.videoName)"></div>
                    <div class="vid_state">
                      <template v-if="item2.type==1">
                        <div class="vid_status status2" v-if="item3.studyStatus==2">已学完</div>
                        <div class="vid_status status1" v-else-if="item3.studyStatus==1">未学完</div>
                        <div class="vid_status status0" v-else>未学习</div>
                      </template>
                      <div class="vid_h1"></div>
                      <div class="vid_thumbs">
                        <img src="@/assets/img/teachroom/thumbs.svg" />
                        <img src="@/assets/img/teachroom/thumbs2sel.svg" />
                        {{item3.likeNum||0}}
                      </div>
                    </div>
                  </div>
                </div>
                <!-- 无视频带概述 -->
                <div class="vid_li" v-if="!item2.videoInfos" @click="gaisuShow" :data-i="index" :data-i2="index2">
                  <div class="vid_img">
                    <img src="@/assets/img/teachroom/novid2.png" class="wk_img" />
                  </div>
                  <div class="vid_rt">
                    <div class="vid_name nowrap2" v-html="ReplaceMathString(item2.knowledgeName)"></div>
                    <div class="vid_state">
                      <div class="vid_status status0"></div>
                      <div class="vid_h1"></div>
                    </div>
                  </div>
                </div>
                <!-- 例题 -->
                <div class="vid_li" v-for="(item3,index3) in item2.exampleVideoInfos" :key="index3" @click="wekePlay2"
                  :data-i="index" :data-i2="index2" :data-i3="index3">
                  <div class="vid_img">
                    <img :src="item3.cover" class="wk_img" v-if="item3.cover" />
                    <img src="@/assets/img/teachroom/novid.png" class="wk_img" v-else />
                  </div>
                  <div class="vid_rt">
                    <div class="vid_name nowrap2" v-html="ReplaceMathString(item3.videoName3)"></div>
                    <div class="vid_state">
                      <div class="vid_status status2" v-if="item3.studyStatus==2">已学完</div>
                      <div class="vid_status status1" v-else-if="item3.studyStatus==1">未学完</div>
                      <div class="vid_status status0" v-else>未学习</div>
                      <div class="vid_h1" v-html="ReplaceMathString(item3.videoName)"></div>
                      <div class="vid_thumbs">
                        <img src="@/assets/img/teachroom/thumbs.svg" />
                        <img src="@/assets/img/teachroom/thumbs2sel.svg" />
                        {{item3.likeNum||0}}
                      </div>
                    </div>
                  </div>
                </div>
              </template>
            </template>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- 购买会员弹窗 -->
  <buyVip :show="state.showVip" @close="quitHide"></buyVip>
  <!-- 积分弹窗 -->
  <coinAlert :show="state.jfShow" :hide="state.jfHide" :num="state.jfNum" :source="state.jfSource" @close="jfHide">
  </coinAlert>
</template>

<script lang="ts" setup>
  import { reactive, onMounted, watch } from 'vue';
  import router from '@/router/index'
  import { ElMessage } from "element-plus"
  import { useUserStore } from "@/store/modules/user"
  import { subjectList } from '@/utils/user/enum'
  import { ReplaceMathString } from '@/utils/user/util'
  import { setStudyTimeApi, analyseVideoNumApi } from "@/api/user"
  import { getVideoListApi, getVideoUrlApi, videoModifyApi, getVideoReviewNumApi, setUserVideoViewNumberApi } from "@/api/video"
  import { useRoute } from "vue-router"
  import buyVip from "@/views/components/buyVip/index.vue"
  import coinAlert from "@/views/components/coinAlert/index.vue"
  import { setLearnKey } from '@/utils/user/learntime'

  defineOptions({
    name: "TeachRoomTeachVideo"
  })

  const route = useRoute()
  const state : any = reactive({
    showVip: false,
    dp: null,
    chapterId: '',
    subActive: '',
    vidInfo: {},
    isFirst: 1,
    isShow: false,
    data: [],
    poster: '',
    videoId: '',
    videoUrl: '',
    videoList: [], //视频
    videoIndex: 0,
    autoplay: false,
    subject: '',
    dataType: 0, //0带概述 1例题
    dataI: 0,
    dataI2: 0,
    dataI3: 0,
    isOne: '', //是否第一个视频
    vidType: '',
    //视频组件
    video: '',
    controls: false,
    videoBtn: true,
    playstate: 0, //0暂停，1播放
    speed: '1.0', //速度
    speedArr: ['0.5', '0.75', '1.0', '1.25', '1.5', '2.0'],
    rate: 0, //显示倍速
    playbtn: true,
    title: '',
    isVidMenu: 0,
    //进度条
    showSlider: true,
    showComp: 0,
    updateState: false,
    slider: 0,
    curtime: 0,
    nowtime: '00:00', // 当前时间
    endtime: '00:00', // 总时长
    duration: '', // 视频长度秒
    isFull: false,
    //积分
    jfShow: false,
    jfHide: true,
    jfNum: '0',
    jfSource: '0',
    // 视频观看时间跟踪
    watchStartTime: 0,
    watchDuration: 0,
    minWatchTime: 30, // 最小观看时间（秒）
    hasEarnedPoints: false // 是否已获得积分
  })

  onMounted(() => {
    init(route.query)
  })

  //监听路由参数
  watch(
    () => route.query,
    (newQ) => {
      if (newQ && route.name == "TeachRoomTeachVideo") {
        init(newQ)
      }
    }
  )

  // 知识点讲解监听
  watch(() => state.vidInfo.summary, (newVal, oldVal) => {
    if (newVal) {
      setJfShow()
    }
  }, { immediate: true })

  // 显示积分-看知识点讲解
  const setJfShow = () => {
    state.jfShow = true
    state.jfHide = false
    state.jfNum = '3'
    state.jfSource = '1'
  }

  // 显示积分-看视频
  const setJfShow2 = () => {
    // 检查是否已获得积分或观看时间不足
    if (state.hasEarnedPoints || state.watchDuration < state.minWatchTime) {
      console.log(`视频观看时间: ${state.watchDuration}秒，最小要求: ${state.minWatchTime}秒，已获得积分: ${state.hasEarnedPoints}`);
      return;
    }
    
    // 标记已获得积分，避免重复获取
    state.hasEarnedPoints = true;
    
    // 显示积分弹窗
    state.jfShow = true;
    state.jfHide = true;
    state.jfNum = '5';
    state.jfSource = '2';
    
    console.log(`视频观看完成，时长${state.watchDuration}秒，获得积分`);
  }

  // 隐藏积分
  const jfHide = () => {
    state.jfShow = false
  }

  // 属性赋值
  const setData = (obj : any) => {
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        state[key] = reactive(obj[key])
      }
    }
  }

  const quitHide = () => {
    state.showVip = false
    if (useUserStore().memberInfo) {
      init(route.query)
    }
  }

  const init = async (query : any) => {
    if (query.id) {
      let { id, type, vid, subKey } = query
      const curVideoInfo = {
          cover: query?.cover,
          isHttp: query?.summary?.indexOf('https://') == 0?1:0,
          likeNum: query?.likeNum,
          score:query?.sore,
          studyStatus: query?.studyStatus,
          summary:query?.summary,
          task: query?.task,
          type: query?.type,
          userCollect: query?.userCollect,
          userLike: query?.userLike,
          videoId:query?.videoId,
          videoName:query?.videoName,
      }
      setData({
        chapterId: id,
        subActive: subKey,
        videoId: vid,
        vidType: type,
        subject: subjectList[subKey].key,
        vidInfo:curVideoInfo
      })
      await getVideoList()
      getVideoUrl()
      //记录学习学科
      setLearnKey(state.subject)
     
    }
  }

  //获取知识点视频列表
  const getVideoList = () => {
    return new Promise(async (resolve, reject) => {
      const { chapterId, videoId, subject } = state
      const param = {
        subject,
        chapterId
      }
      getVideoListApi(param)
        .then((res : any) => {
          const res2 = res.data
          let vidInfo = ''
          if (res2?.length) {
            for (const i of res2) {
              let num = 1
              for (const x of i.knowledgeList) {
                x.num = num
                num++
                if (x.exampleVideoInfos) {
                  //例题
                  const name = x.videoInfos[0]?.videoName || ''
                  for (const y of x.exampleVideoInfos) {
                    //知识点(例题)-收藏用
                    y['videoName3'] = `${name}(${y.videoName})`
                    y.type = x.type
                    y.isHttp = 0
                    y.summary = x.summary
                    if (x.summary && x.summary.indexOf('https://') == 0) {
                      y.isHttp = 1
                    }
                    //匹配播放中视频信息
                    if (y.videoId == videoId) {
                      y.type = x.type
                      let url = x.summary
                      y.summary = url
                      y.isHttp = 0
                      if (url && url.indexOf('https://') == 0) {
                        y.isHttp = 1
                      }
                      if (!vidInfo) {
                        setData({
                          vidInfo: y
                        })
                      }
                    }
                  }
                }
                if (x.videoInfos) {
                  for (const z of x.videoInfos) {
                    z.type = x.type
                    z.summary = x.summary
                    z.isHttp = 0
                    if (x.summary && x.summary.indexOf('https://') == 0) {
                      z.isHttp = 1
                    }
                    //匹配播放中视频信息
                    if (z.videoId == videoId) {
                      z.type = x.type
                      let url = x.summary
                      z.summary = url
                      z.isHttp = 0
                      if (url && url.indexOf('https://') == 0) {
                        z.isHttp = 1
                      }
                      if (!vidInfo) {
                        setData({
                          vidInfo: z
                        })
                      }
                    }
                  }
                }
              }
            }
            setData({
              data: res2
            })
          
          } else {
            setData({
              title: '',
              data: [],
              videoId: '',
              isShow: true
            })
          }
          resolve(true)
        }).catch(err => {
          reject(err)
        })
    })
  }

  //判断视频播放次数
  const getVideoUrl = () => {
    if (useUserStore().memberInfo) {
      getVidSrc()
    } else {
      getVideoReviewNumApi().then((res : any) => {
        const num = res.data
        if (num < 2) {
          ElMessage.success(`剩余免费观看次数：${2 - num - 1}`)
          getVidSrc()
        } else {
          setData({
            isShow: true
          })
          //开通会员弹窗
          state.showVip = true
        }
      })
    }
  }
  //获取知识点视频url
  const getVidSrc = () => {
    const param = {
      type: state.vidType, //1优学派 2菁优网
      videoId: state.videoId
    }
    
    // 重置观看时间计时器和积分状态
    state.watchStartTime = 0;
    state.watchDuration = 0;
    state.hasEarnedPoints = false;
    
    getVideoUrlApi(param)
      .then((res : any) => {
        let data = res.data || ''
        setData({
          isShow: true,
          videoUrl: data,
          videoList: data ? [data] : []
        })
        if (state.isFirst) {
          //初始化视频控件
          initPlayers()
        } else {
          switchVideo()
        }
        setData({
          isFirst: 0
        })
        if (data) {
          videoPause()
        }
        setVideo()
        setStudyState(1)
        videoScoreSave()
        
      })
      .catch(() => {
        setData({
          isShow: true
        })
      })
  }
  //设置视频信息,默认第1个
  const setVideo = () => {
    const videos = state.videoList
    if (videos.length) {
      setData({
        videoUrl: videos[0],
        autoplay: true
      })
      videoPlay()
    } else {
      setData({
        videoUrl: '',
        autoplay: false
      })
    }
  }

  //记录视频播放次数
  const setUserVideoViewNumber = () => {
    const { chapterId, pointId, videoId, subject } = state
    const param = {
      time: 0,
      videoId,
      type: state.vidType, //1优学派 2菁优网
      subject,
      pointId,
      viewType: true, //true:观看次数+1；false:增加观看次数
      chapterId: ''
    }
    if (chapterId && chapterId != 'undefined') {
      param.chapterId = chapterId
    }
    if (!videoId) {
      return
    }
    setUserVideoViewNumberApi(param)
  }
  // 记录每日查看视频数量
  const analyseVideoNum = () => {
    const { subject } = state
    analyseVideoNumApi({ subject })
  }
  //记录视频播放状态
  const videoScoreSave = async() => {
    await analyseVideoNum()
    //  setUserVideoViewNumber()
    setTimeout(() => {
      setUserVideoViewNumber()
    }, 2000);
   
  }
  //返回置顶
  const setTop = () => {
    const div : any = document.getElementById('content');
    div.scrollTop = 0;
  }

  // 知识点-切换视频
  const wekePlay = (e : any) => {
    const { i, i2, i3 } = e.currentTarget.dataset
    const { data } = state
    const info = data[i].knowledgeList[i2].videoInfos[i3]
    const { cover, videoId, videoName, type } = info
    if (videoId == state.videoId) {
      return
    }
    //改变学习状态
    if (info.studyStatus != 2) {
      info.studyStatus = 1
    }
    data[i].knowledgeList[i2].videoInfos[i3] = info
    setData({
      poster: cover,
      videoId,
      title: videoName,
      data,
      dataI: i,
      dataI2: i2,
      dataI3: i3,
      dataType: 0,
      isOne: 0,
      vidType: type
    })
    //跳转第0秒
    state.dp?.seek(0)
    
    // 重置观看时间计时器和积分状态
    state.watchStartTime = 0;
    state.watchDuration = 0;
    state.hasEarnedPoints = false;
    console.log('切换知识点视频，重置观看时间计时器和积分状态');
    
    setData({
      slider: 0,
      vidInfo: info
    })
    getVideoUrl()
  }
  // 知识点例题-切换视频
  const wekePlay2 = (e : any) => {
    const { i, i2, i3 } = e.currentTarget.dataset
    const { data } = state
    const info = data[i].knowledgeList[i2].exampleVideoInfos[i3]
    const { cover, videoId, videoName3, type } = info
    if (videoId == state.videoId) {
      return
    }
    //改变学习状态
    if (info.studyStatus != 2) {
      info.studyStatus = 1
    }
    data[i].knowledgeList[i2].exampleVideoInfos[i3] = info
    setData({
      poster: cover,
      videoId,
      title: videoName3,
      data,
      dataI: i,
      dataI2: i2,
      dataI3: i3,
      dataType: 1,
      isOne: 0,
      vidType: type
    })
    //跳转第0秒
    state.dp?.seek(0)
    
    // 重置观看时间计时器和积分状态
    state.watchStartTime = 0;
    state.watchDuration = 0;
    state.hasEarnedPoints = false;
    console.log('切换例题视频，重置观看时间计时器和积分状态');
    
    setData({
      slider: 0,
      vidInfo: info
    })
    getVideoUrl()
  }
  // 设置学习状态
  const setStudyState = (status : any) => {
    console.log(status,"statusstatusstatus")

    const { chapterId, pointId, videoId, subject, data, dataI, dataI2, dataI3, dataType, title, vidType, vidInfo } = state
    const param = {
      chapterId,
      pointId,
      videoId,
      type: vidType, //1优学派 2菁优网
      subject,
      source: vidType, //1同步章节 2知识点
      knowledgeName: title,
      status //0未学习 1正在学 2已学完
    }
    //非已学完判断
    const state2 =
      dataType == 0
        ? data[dataI].knowledgeList[dataI2].videoInfos[dataI3].studyStatus
        : data[dataI].knowledgeList[dataI2].exampleVideoInfos[dataI3].studyStatus
    if (vidInfo?.studyStatus != 2) {
      videoModifyApi(param)
        .then((curData:any) => {
          console.log("data---",curData)
          // if (dataType == 0) {
          //   //概述
          //   data[dataI].knowledgeList[dataI2].videoInfos[dataI3].studyStatus = status
          // } else {
          //   //例题
          //   data[dataI].knowledgeList[dataI2].exampleVideoInfos[dataI3].studyStatus = status
          // }
          //改变学习状态
          vidInfo.studyStatus = curData?.data?.status
          setData({
            data,
            vidInfo
          })
        })
    }
  }
  //目录-点击
  const setCataVid = (e : any) => {
    const { i } = e.currentTarget.dataset
    const { data } = state
    for (const x of data) {
      x.active = ''
    }
    //默认第一个展开
    data[i].active = 'active'
    const list = data[i].knowledgeList
    list[0].active = 'up'
    //获取视频标题
    const info = list[0]?.videoInfos || []
    const title = info[0]?.videoName || ''
    const videoId = info[0]?.videoId || ''
    setData({
      title,
      data,
      videoId,
      isShow: true
    })
    //获取第一个视频
    if (videoId) {
      setData({
        dataI: i,
        dataI2: 0,
        dataI3: 0,
        isOne: 1
      })
      getVideoUrl()
    } else {
      //无视频显示概述,清空视频
      setData({
        dataI: i,
        videoId: '',
        videoUrl: ''
      })
    }
  }
  //概述-显示(只能跳转h5)
  const gaisuShow = (e : any) => {
    const { i, i2 } = e.currentTarget.dataset
    const { data, subject } = state
    const url = data[i].knowledgeList[i2].summary || ''
    if (url) {
      if (url.indexOf('https://') == 0) {
        router.push({ name: "TeachRoomTeachView", query: { url, subject } })
      } else {
        //html代码
        localStorage.explainStr = url
        router.push({ name: "TeachRoomTeachView", query: { subject } })
      }
    } else {
      //无知识点
      localStorage.explainStr = ''
      router.push({ name: "TeachRoomTeachView", query: { subject } })
    }
  }

  // 点赞
  const setThumbs = () => {
    const { chapterId, pointId, videoId, subject, data, dataI, dataI2, dataI3, vidInfo, title, vidType } = state
    const param : any = {
      chapterId,
      pointId,
      videoId,
      type: vidType, //1优学派 2菁优网
      subject,
      source: vidType, //1同步章节 2知识点
      knowledgeName: title
    }
    const isThumbs = state.vidInfo.userLike ? 0 : 1
    ElMessage.closeAll()
    if (isThumbs) {
      param.like = 1
      videoModifyApi(param)
        .then(() => {
          ElMessage.success('点赞成功')
          // if (vidInfo.videoName3) {
          //   //例题
          //   data[dataI].knowledgeList[dataI2].exampleVideoInfos[dataI3].likeNum++
          //   data[dataI].knowledgeList[dataI2].exampleVideoInfos[dataI3].userLike = true
          // } else {
          //   //概述
          //   data[dataI].knowledgeList[dataI2].videoInfos[dataI3].likeNum++
          //   data[dataI].knowledgeList[dataI2].videoInfos[dataI3].userLike = true
          // }
          state.vidInfo.likeNum++
          state.vidInfo.userLike = true
          setData({
            data
          })
        })
    } else {
      param.like = 0
      videoModifyApi(param)
        .then(() => {
          ElMessage.success('已取消点赞')
          // if (vidInfo.videoName3) {
          //   //例题
          //   data[dataI].knowledgeList[dataI2].exampleVideoInfos[dataI3].likeNum--
          //   data[dataI].knowledgeList[dataI2].exampleVideoInfos[dataI3].userLike = false
          // } else {
          //   //概述
          //   data[dataI].knowledgeList[dataI2].videoInfos[dataI3].likeNum--
          //   data[dataI].knowledgeList[dataI2].videoInfos[dataI3].userLike = false
          // }
          state.vidInfo.likeNum--
          state.vidInfo.userLike = false
          setData({
            data
          })
        })
    }
  }
  // 收藏
  const setCollect = () => {
    const { chapterId, pointId, videoId, subject, data, dataI, dataI2, dataI3, vidInfo, vidType } = state
    const param : any = {
      chapterId,
      pointId,
      videoId,
      type: vidType, //1优学派 2菁优网
      subject,
      source: vidType //1同步章节 2知识点
    }
    param.knowledgeName = vidInfo.videoName3 || vidInfo.videoName
    const isCollect = state.vidInfo.userCollect ? 0 : 1
    if (isCollect) {
      param.collect = 1
      videoModifyApi(param)
        .then(() => {
          ElMessage.success('收藏成功')
          if (vidInfo.videoName3) {
            //例题
            data[dataI].knowledgeList[dataI2].exampleVideoInfos[dataI3].userCollect = true
            state.vidInfo.userCollect = true
          } else {
            //概述
            data[dataI].knowledgeList[dataI2].videoInfos[dataI3].userCollect = true
            state.vidInfo.userCollect = true
          }
          setData({
            data
          })
        })
    } else {
      param.collect = 0
      videoModifyApi(param)
        .then(() => {
          ElMessage.success('已取消收藏')
          if (vidInfo.videoName3) {
            //例题
            data[dataI].knowledgeList[dataI2].exampleVideoInfos[dataI3].userCollect = false
            state.vidInfo.userCollect = false
          } else {
            //概述
            data[dataI].knowledgeList[dataI2].videoInfos[dataI3].userCollect = false
            state.vidInfo.userCollect = false
          }
          setData({
            data
          })
        })
    }
  }

  //组件-播放
  const videoPlay = () => {
    if (state.videoUrl) {
      // state.dp.play()
      //自动播放-延迟且用户点击
      setTimeout(()=>{
        let video:any=document.getElementById('video')
        video.play()
      },100)
      setData({
        videoBtn: false
      })
    } else {
      ElMessage.error('请先选择一个视频')
    }
  }

  //组件-暂停
  const videoPause = () => {
    state.dp.pause()
    setData({
      videoBtn: true
    })
  }

  function initPlayers() {
    let { videoName3, videoName, type, cover } = state.vidInfo
    let window1 : any = window
    let DPlayer : any = window1?.DPlayer
    state.dp = new DPlayer({
      container: document.getElementById('dplayer1'),
      autoplay: true, //没效果
      theme: '#1DDFAC', //进度条、音量颜色
      preload: 'auto',
      volume: 1,
      playbackRates: [0.5, 0.75, 1, 1.25, 1.5, 2],
      contextmenu: [],
      video: {
        title: videoName3 || videoName,
        url: state.videoUrl,
        pic: cover,
        type: type == 1 ? 'hls' : 'normal'
      }
    });

    state.dp.on('loadstart', function () {
      showPlayIcon()
      // 确保倍速菜单正确创建（延长等待时间确保DPlayer完全加载）
      setTimeout(() => {
        createCustomSpeedMenu()
      }, 1000)
      
      // 修改设置为倍速弹窗
      let setbtn : any = document.querySelector('.dplayer-setting-icon')
      setbtn.addEventListener('mouseenter', function () {
        if (document.querySelectorAll('.dplayer-hide-controller').length) {
          // 底部菜单隐藏时不显示
          return
        }
        //显示倍速弹窗
        let setting : any = document.querySelector('.dplayer-setting-box')
        setting.className =
          'dplayer-setting-box dplayer-setting-box-narrow dplayer-setting-box-speed dplayer-setting-box-open'
      })
      //倍速弹窗hover隐藏
      let setting : any = document.querySelector('.dplayer-setting-box')
      setting.addEventListener('mouseleave', function () {
        setting.className = 'dplayer-setting-box'
      })
      setting.addEventListener('click', function () {
        setting.className = 'dplayer-setting-box'
      })

      //隐藏视频标题
      let vidClass : any = document.querySelector('.dplayer-video-current')
      vidClass.addEventListener('mouseleave', function () {
        if (document.querySelectorAll('.dplayer-paused').length) {
          // 暂停时不隐藏
          return
        }
        let title : any = document.getElementById('fulltit')
        title.style.display = 'none'
      })
    })

    //设置倍速文字
    state.dp.on('ratechange', function () {
      speedChange('')
    });

          // 确保倍速选项初始化
      setTimeout(() => {
        speedChange(1) // 初始化为正常倍速
        // 调试信息
        console.log('DPlayer初始化完成，当前playbackRates:', [0.5, 0.75, 1, 1.25, 1.5, 2])
        console.log('DPlayer实例:', state.dp)
      }, 100);

    state.dp.on('ended', function () {
      // 视频结束时，计算总观看时间
      const endTime = Date.now();
      state.watchDuration = Math.floor((endTime - state.watchStartTime) / 1000);
      console.log('视频结束，总观看时间:', state.watchDuration, '秒');
      
      videoPause()
      setStudyState(2)
      setJfShow2() // 调用积分逻辑，内部会检查观看时间
    });
    
    state.dp.on('pause', function () {
      // 暂停时，更新观看时间
      if (state.watchStartTime > 0) {
        const pauseTime = Date.now();
        state.watchDuration += Math.floor((pauseTime - state.watchStartTime) / 1000);
        console.log('视频暂停，当前累计观看时间:', state.watchDuration, '秒');
      }
      showPlayIcon()
    });
    
    state.dp.on('play', function () {
      // 播放时，重置开始时间
      state.watchStartTime = Date.now();
      console.log('视频开始播放，重置计时器');
      hidePlayIcon()
    });

    state.dp.on('volumechange', function () {
    })
  }

  fullScreenListeners()
  //监听全屏
  function fullScreenListeners() {
    document.addEventListener('fullscreenchange', function () {
      let fullIcon : any = document.querySelector('#fullIcon')
      if (document.fullscreenElement) {
        fullIcon.setAttribute('data-title', '退出全屏')
      } else {
        fullIcon.setAttribute('data-title', '全屏')
      }
    })
  }

  //显示播放图标
  function showPlayIcon() {
    let icon : any = document.querySelector('.dplayer-bezel-icon')
    if(icon){
      icon.className = 'dplayer-bezel-icon play'
    }
  }

  //隐藏播放图标
  function hidePlayIcon() {
    setTimeout(() => {
      let icon : any = document.querySelector('.dplayer-bezel-icon')
      if(icon){
        icon.className = 'dplayer-bezel-icon'
      }
    }, 0)
  }

  //切换视频
  function switchVideo() {
    let { videoName3, videoName, type, cover } = state.vidInfo
    let obj = {
      title: videoName3 || videoName,
      url: state.videoUrl,
      pic: cover,
      type: type == 1 ? 'hls' : 'normal'
    }
    let src = state.dp.video.src
    if (src == obj.url) {
      return
    }
    
    // 切换视频时重置计时器和积分状态
    state.watchStartTime = 0;
    state.watchDuration = 0;
    state.hasEarnedPoints = false;
    console.log('切换视频，重置观看时间计时器和积分状态');
    
    state.dp.switchVideo(obj);
    speedChange(1)
    // 切换视频后重新创建倍速菜单
    setTimeout(() => {
      createCustomSpeedMenu()
    }, 500)
    //自动播放-必须延迟且是用户点击
    setTimeout(()=>{
      let video:any=document.getElementById('video')
      video.play()
    },100)
  }
  // 倍速切换
  function speedChange(num : any) {
    let video : any = document.getElementById('video')
    let speed = num || (video ? video.playbackRate : 1)
    let sptxt : any = document.querySelector('.dplayer-icon.dplayer-setting-icon')
    
    if (sptxt) {
      if (speed == 1) {
        sptxt.innerHTML = '倍速'
      } else if (speed == 2) {
        sptxt.innerHTML = '2.0X'
      } else if (speed == 1.25) {
        sptxt.innerHTML = '1.25X'
      } else if (speed == 1.5) {
        sptxt.innerHTML = '1.5X'
      } else if (speed == 0.75) {
        sptxt.innerHTML = '0.75X'
      } else if (speed == 0.5) {
        sptxt.innerHTML = '0.5X'
      } else {
        sptxt.innerHTML = speed + 'X'
      }
    }
    
    //倍速弹窗选中变色 - 只处理自定义倍速项
    let list : any = document.querySelectorAll('.dplayer-setting-speed-item.custom-speed')
    for (let i = 0; i < list.length; i++) {
      let itemSpeed = list[i].attributes['data-speed']
      if (itemSpeed) {
        let num = Number(itemSpeed.value)
        if (Math.abs(num - speed) < 0.01) { // 使用小数比较避免精度问题
          list[i].className = 'dplayer-setting-speed-item custom-speed green'
        } else {
          list[i].className = 'dplayer-setting-speed-item custom-speed'
        }
      }
    }
    
    console.log(`当前倍速: ${speed}x`)
  }
  // 修复并优化倍速菜单
  function createCustomSpeedMenu() {
    // 避免重复创建，先清除所有现有的倍速项
    const existingItems = document.querySelectorAll('.dplayer-setting-speed-item')
    existingItems.forEach(item => item.remove())
    
    // 查找DPlayer原生的设置容器
    let settingBox = document.querySelector('.dplayer-setting-box')
    if (!settingBox) {
      console.log('未找到DPlayer设置容器')
      return
    }
    
    // 检查是否有现有的speeds容器，如果没有就创建
    let speedContainer = settingBox.querySelector('.dplayer-setting-speeds')
    if (!speedContainer) {
      speedContainer = document.createElement('div')
      speedContainer.className = 'dplayer-setting-speeds'
      settingBox.appendChild(speedContainer)
    }
    
    // 清空容器
    speedContainer.innerHTML = ''
    
    // 倍速选项数组
    const speeds = [
      { value: 0.5, text: '0.5X' },
      { value: 0.75, text: '0.75X' },
      { value: 1, text: '正常' },
      { value: 1.25, text: '1.25X' },
      { value: 1.5, text: '1.5X' },
      { value: 2, text: '2.0X' }
    ]
    
    // 创建倍速选项
    speeds.forEach(speed => {
      const speedItem = document.createElement('div')
      speedItem.className = 'dplayer-setting-speed-item custom-speed'
      speedItem.setAttribute('data-speed', speed.value.toString())
      speedItem.textContent = speed.text
      
      // 添加点击事件
      speedItem.addEventListener('click', function() {
        if (state.dp && state.dp.video) {
          state.dp.video.playbackRate = speed.value
          speedChange(speed.value)
          
          // 隐藏设置菜单
          settingBox.className = 'dplayer-setting-box'
          
          console.log(`倍速已切换到: ${speed.value}x`)
        }
      })
      
      speedContainer.appendChild(speedItem)
    })
    
    console.log('优化后的倍速菜单创建完成')
    
    // 初始化当前倍速显示
    speedChange(1)
  }

  //视频控件e
</script>

<style lang="scss" scoped>
  @import url('@/assets/styles/reset.css');

  .header_seat {
    width: 100%;
    height: 4.375rem;
    float: left;
  }

  .none {
    display: none !important
  }

  .nowrap {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .nowrap2 {
    width: 100%;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    word-break: break-all;
  }

  .content {
    width: 100%;
    background: #F5F5F5;
    overflow-y: auto;
  }

  .inner {
    margin: 0 auto;
    width: 81.25rem;
  }

  .wrap {
    float: left;
    width: 100%;
    /* height: calc(100vh - 7.4375rem);
    overflow-y: auto; */
  }

  .wrap>div {
    /* height: calc(100vh - 7.4375rem);
    overflow-y: auto; */
    border: .0625rem solid #eaeaea;
    box-sizing: border-box;
    background: #fff;
  }

  /* 视频 */
  .menu_lt {
    float: left;
    width: calc(100% - 21.125rem - .625rem);
    padding: .625rem;
  }

  .wk_box {
    width: 100%;
  }

  .wk_box div {
    float: left;
  }

  .wk_tit {
    width: 100%;
  }

  .wk_p {
    line-height: 1.375rem;
    color: #2a2b2a;
    font-size: 1rem;
    margin: 0 .625rem 0 0;
  }

  .wk_status {
    width: 2.875rem;
    line-height: 1.375rem;
    border-radius: .25rem;
    text-align: center;
    font-size: .75rem;
  }

  .wk_status.status0 {
    color: #fff;
    background: #999;
  }

  .wk_status.status1 {
    color: #EF9D19;
    background: #FEF8E9;
  }

  .wk_status.status2 {
    color: #009C7F;
    background: #E5F9F6;
  }

  .wk_video {
    width: 100%;
    height: 41.8125rem;
    background: #000;
    margin: 1.25rem 0 0;
  }

  .wk_nodata {
    width: 100%;
    height: 41.8125rem;
    background: #000;
    margin: 1.25rem 0 0;
    color: #999999;
    font-size: .875rem;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-flow: column;
  }

  .wk_nodata img {
    width: 7.4375rem;
    height: 8rem;
    margin: 0 0 .625rem;
  }

  /* 点赞收藏 */
  .wk_opt {
    width: 100%;
    height: 4rem;
    box-sizing: border-box;
    padding: 1.25rem .625rem;
    border-bottom: .0625rem solid #eaeaea;
  }

  .wk_collect {
    margin: 0 3.125rem 0 0;
    cursor: pointer;
  }

  .wk_collect img {
    width: 1.5rem;
    height: 1.5rem;
  }

  .wk_collect img:first-child,
  .wk_collect.active img:last-child {
    display: inline-block;
  }

  .wk_collect img:last-child,
  .wk_collect.active img:first-child {
    display: none;
  }

  .wk_thumbs {
    line-height: 1.5rem;
    color: #666666;
    font-size: .875rem;
    float: right;
    cursor: pointer;
  }

  .wk_thumbs img {
    float: left;
    width: 1.5rem;
    height: 1.5rem;
    margin: 0 .375rem 0 0;
  }

  .wk_thumbs img:nth-child(1),
  .wk_thumbs.active img:nth-child(2) {
    display: inline-block;
  }

  .wk_thumbs img:nth-child(2),
  .wk_thumbs.active img:nth-child(1) {
    display: none;
  }

  /* 知识点 */
  .iframe {
    border: 0;
    width: 100%;
    height: 100vh;
  }

  .wkinfo {
    float: left;
    width: 100%;
    color: #2a2b2a;
    font-size: .875rem;
    font-weight: 400;
    line-height: 1.625rem;
    box-sizing: border-box;
    padding: .625rem 1.25rem;
  }

  /* 正在播放 */
  .menu_rt {
    float: right;
    width: 21.125rem;
    border: .0625rem solid #eaeaea;
    background: #ffffff;
  }

  .menu_rt div {
    float: left;
  }

  .now_box {
    width: 100%;
    border-bottom: .0625rem solid #eaeaea;
  }

  .now_air {
    width: 100%;
    line-height: 1rem;
    color: #5a85ec;
    font-size: 1rem;
    margin: .8125rem 0 1.25rem;
  }

  .now_air img {
    float: left;
    width: 1rem;
    height: 1rem;
    margin: 0 .375rem 0 .625rem;
  }

  .now_img {
    width: 19.875rem;
    height: 11.1875rem;
    border-radius: .25rem;
    border: .0625rem solid #5a85ec;
    box-sizing: border-box;
    overflow: hidden;
    margin: 0 .625rem;
  }

  .now_img img {
    float: left;
    width: 19.875rem;
    height: 11.1875rem;
  }

  .now_name {
    width: 100%;
    line-height: 1.1875rem;
    color: #5a85ec;
    font-size: .875rem;
    box-sizing: border-box;
    padding: .375rem .625rem 1.25rem;
  }

  div.now_status {
    float: right;
    width: 3.75rem;
    line-height: 1.5rem;
    border-radius: 0 0 0 .25rem;
    text-align: center;
    font-size: .75rem;
    color: #fff;
    position: relative;
    z-index: 2;
    margin: -11.1875rem 0 0;
  }

  .now_status.status0 {
    background: #999;
  }

  .now_status.status1 {
    background: linear-gradient(150.8deg, #f6d22b 0%, #f29500 100%);
  }

  .now_status.status2 {
    background: linear-gradient(166.7deg, #08d8b8 0%, #00b392 100%);
  }

  /* 列表 */
  .vid_h2 {
    width: calc(100% - 1.25rem);
    line-height: 1.1875rem;
    border-radius: .25rem;
    background: #f5f5f5;
    color: #2a2b2a;
    font-size: .875rem;
    box-sizing: border-box;
    padding: .5625rem .625rem;
    margin: .625rem 0 0 .625rem;
  }

  .vid_ul {
    width: 100%;
    box-sizing: border-box;
    padding: .625rem;
  }

  .vid_li {
    width: 100%;
    height: 5.0625rem;
    overflow: hidden;
    margin: 0 0 .625rem;
    cursor: pointer;
  }

  .vid_li:last-child {
    margin: 0;
  }

  .vid_img,
  .vid_img img {
    float: left;
    width: 9rem;
    height: 5.0625rem;
    border-radius: .25rem;
  }

  .vid_rt {
    width: 10.375rem;
    margin: 0 0 0 .375rem;
  }

  .vid_name {
    width: 10.5rem;
    max-height: 4rem;
    line-height: 1.5;
    color: #2a2b2a;
    font-size: .75rem;
  }

  .vid_li .vid_name {
    color: #5A85EC;
  }

  .vid_state {
    width: 100%;
    margin: 1rem 0 0;
  }

  .vid_status {
    width: 2.875rem;
    line-height: 1.3125rem;
    border-radius: .25rem;
    text-align: center;
    color: #ffffff;
    font-size: .75rem;
  }

  .vid_status.status0 {
    color: #fff;
    background: #999;
  }

  .vid_status.status1 {
    color: #EF9D19;
    background: #FEF8E9;
  }

  .vid_status.status2 {
    color: #009C7F;
    background: #E5F9F6;
  }

  .vid_h1 {
    line-height: 1.3125rem;
    color: #999999;
    font-size: .75rem;
    margin: 0 .3125rem 0 .625rem;
  }

  div.vid_thumbs {
    float: right;
    line-height: 1.125rem;
    color: #666666;
    font-size: .75rem;
    float: right;
  }

  .vid_thumbs img {
    float: left;
    width: 1.125rem;
    height: 1.125rem;
    margin: 0 .375rem 0 0;
  }

  .vid_thumbs img:nth-child(1),
  .vid_thumbs.active img:nth-child(2) {
    display: inline-block;
  }

  .vid_thumbs img:nth-child(2),
  .vid_thumbs.active img:nth-child(1) {
    display: none;
  }

  .vid_tit:hover,
  .vid_thumbs img:hover {
    cursor: pointer;
  }

  /* 优化后的DPlayer倍速样式 */
  :deep(.dplayer-setting-speeds) {
    display: block;
    padding: 5px 0;
  }

  :deep(.dplayer-setting-speed-item.custom-speed) {
    padding: 6px 16px;
    cursor: pointer;
    color: #eee;
    line-height: 1.2;
    font-size: 13px;
    text-align: center;
    margin: 1px 0;
    transition: all 0.2s ease;
    background: #302f27;
    border: none;
    width: 100%;
    box-sizing: border-box;
  }

  :deep(.dplayer-setting-speed-item.custom-speed:hover) {
    background-color: #333;
    color: red;
  }

  :deep(.dplayer-setting-speed-item.custom-speed.green) {
    color: #1DDFAC !important;
    background-color: #999;
    font-weight: 500;
  }

  /* 隐藏原有的DPlayer倍速控制，避免重复 */
  :deep(.dplayer-setting-speed-item:not(.custom-speed)) {
    display: none !important;
  }

  // /* 隐藏悬停时显示的标题 */
// :deep(.dplayer-controller .dplayer-icons .dplayer-icon.dplayer-comment-icon) {
//   display: none !important;
// }

// :deep(.dplayer-controller .dplayer-icons-right .dplayer-icon.dplayer-full-icon) {
//   margin-right: 0;
// }

// :deep(.dplayer-controller .dplayer-icons .dplayer-label) {
//   display: none !important;
// }

// :deep(.dplayer-controller .dplayer-icons #dplayer-menu) {
//   display: none !important;
// }

// :deep(.dplayer-info-panel) {
//   display: none !important;
// }

// /* 隐藏标题相关的所有元素 */
// :deep(.dplayer-controller-mask) ~ div[id^="fulltit"] {
//   display: none !important;
// }

:deep(#fulltit) {
  display: none !important;
}
</style>
