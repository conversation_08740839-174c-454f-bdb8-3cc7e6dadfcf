import { request } from "@/utils/axios"
const SDKPORT = "/api/xiaoyeoo/sdkapi/point"


/** 查询教材标熟情况 */
export function getPointCategoryApi(params: Object) {
  return request<IApiResponseData<IVersionInfo[]>>({
    url: `/api/xiaoyeoo/point/category/getPointCategory`,
    method: "GET",
    params
  })
}

// 添加标熟
export function categoryAddApi(data: Object) {
  return request<IApiResponseData<IVersionInfo[]>>({
    url: `/api/xiaoyeoo/point/category/add`,
    method: "POST",
    data
  })
}
// 删除标熟
export function categoryDelApi(data: Object) {
  return request<IApiResponseData<IVersionInfo[]>>({
    url: `/api/xiaoyeoo/point/category/del`,
    method: "POST",
    data
  })
}
// 知识点标熟(返回训练id)
export function addTrainingApi(data: Object) {
  return request<IApiResponseData<IVersionInfo[]>>({
    url: `/api/xiaoyeoo/point/addTraining`,
    method: "POST",
    data
  })
}

// 知识点标熟提交训练
export function saveTrainingApi(data: Object) {
  return request<IApiResponseData<IVersionInfo[]>>({
    url: `/api/xiaoyeoo/point/category/saveTraining`,
    method: "POST",
    data
  })
}

// 知识点标熟训练报告
export function getTrainingReportApi(params: Object) {
  return request<IApiResponseData<IVersionInfo[]>>({
    url: `/api/xiaoyeoo/point/category/getTrainingReport`,
    method: "GET",
    params
  })
}

// // 创建训练(返回训练id)
// export function addTrainingApi(data: Object) {
//   return request<IApiResponseData<IVersionInfo[]>>({
//     url: `/api/xiaoyeoo/point/addTraining`,
//     method: "POST",
//     data
//   })
// }

// 保存训练
// export function saveTrainingApi(data: Object) {
//   return request<IApiResponseData<IVersionInfo[]>>({
//     url: `/api/xiaoyeoo/point/point/saveTraining`,
//     method: "POST",
//     data
//   })
// }

// 查询训练知识点内容
export function trainingInfoApi(params: Object) {
  return request<IApiResponseData<IVersionInfo[]>>({
    url: `/api/xiaoyeoo/point/trainingInfo`,
    method: "GET",
    params
  })
}


// 精准练保存训练
export function savePointTrainingApi(data: Object) {
  return request<IApiResponseData<IVersionInfo[]>>({
    url: `/api/xiaoyeoo/point/saveTraining`,
    method: "POST",
    data
  })
}