<template>
    <div class="flex-box">
        <div class="con-box">
            <el-table
                :data="tableData"
                style="width: 100%">
                <el-table-column type="index" label="序号" width="100" />
                <el-table-column prop="name" label="科目" align="left" width="180"></el-table-column>
                <el-table-column prop="date" label="闯关内容" align="left" width="220"></el-table-column>
                <el-table-column label="闯关时间" align="left" prop="address"></el-table-column>
                <el-table-column label="正确率" align="left" prop="address">
                    <template #default="scope">
                        <div v-if="scope.row.address == '0'" class="flex-img">
                            <span>{{scope.row.address}}% 闯关成功</span>
                            <img src="@/assets/img/percision/final_succ_icon.png" />
                        </div>
                        <span class="red-text">{{scope.row.address}}% 闯关失败</span>
                    </template>
                </el-table-column>
                <el-table-column label="查看">
                    <template #default="scope">
                        <span class="blue-text" @click="openDetail(scope.row)">查看详情 ></span>
                    </template>
                </el-table-column>
            </el-table>
            <Pagination
                :total="pageData.total"
                :current="pageData.current"
                :layout="'total, prev, pager, next, jumper'"
                class="page-box"
                @currentSizeChange="currentSizeChange1" />

        </div>
    </div>
</template>
<script setup lang="ts">
import { reactive } from 'vue'

const tableData = [
    { name: '王小虎', date: '2021-01-01', address: '0' },
]
class IPage {
    total = 0
    current = 1
    size = 10
}
let pageData = reactive(new IPage())
const currentSizeChange1 = (currentPage: number, pageSize: number) => {
  pageData.current = currentPage
  pageData.size = pageSize
}
const openDetail = (row) => {
    console.log(row)
}
</script>
<style lang="scss" scoped>
:deep(.el-table) {
    height: calc(100vh - var(--v3-navigationbar-height) - 168px);
    .cell {
        font-size: 16px;
    }
}
.flex-box {
    display: flex;
    justify-content: center;
    width: 100%;
    background: #f5f5f5;
}
.con-box {
    width: 1300px;
    background-color: white;
    padding: 30px 20px;
}
.flex-img {
    display: flex;
    align-items: center;
    img {
        width: 36px;
        height: 30px;
        margin-left: 6px;
    }
    span {
        font-size: 16px;
    }
}
.red-text {
    color: #dd2a2a;
}
.blue-text {
    color: #009c7f;
    cursor: pointer;
    font-weight: 400;
}
.page-box {
    margin-top: 20px;
}
</style>