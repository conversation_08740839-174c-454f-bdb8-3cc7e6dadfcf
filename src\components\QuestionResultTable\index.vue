<template>
  <div class="question-result-table">
    <div class="result-table">
      <div class="table-header">
        <div class="header-row">
          <div class="header-cell first-cell">题号</div>
          <div v-for="(item, index) in questionItems" :key="`header-${index}`" class="header-cell">
            {{ index + 1 }}
          </div>
        </div>
      </div>
      <div class="table-body">
        <div class="body-row">
          <div class="body-cell first-cell">答题情况</div>
          <div v-for="(item, index) in questionItems" :key="`body-${index}`" class="body-cell">
            <div class="status-icon" :class="getStatusClass(item)">
              <el-icon v-if="getStatusClass(item) === 'status-correct'"><Check /></el-icon>
              <el-icon v-else-if="getStatusClass(item) === 'status-wrong'"><Close /></el-icon>
              <el-icon v-else><WarningFilled /></el-icon>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { Check, Close, WarningFilled } from '@element-plus/icons-vue';

defineProps({
  questionItems: {
    type: Array,
    required: true,
    default: () => []
  }
});

// 判断题目状态并返回对应的CSS类
const getStatusClass = (item: any) => {
  // 判断是否正确
  const userAnswer = item.userJson ? item.userJson[0] : null;
  const correctAnswer = item.displayAnswer || '';
  
  // 将字母答案转为数字索引
  const letterToIndex = (letter: string) => {
    return letter.charCodeAt(0) - 65; // A=>0, B=>1, C=>2, D=>3...
  };
  
  // 比较用户答案和正确答案
  const isCorrect = () => {
    if (!userAnswer) return false;
    
    // 如果是选择题，比较索引
    if (item.cate === 1) {
      const correctIndex = letterToIndex(correctAnswer);
      return userAnswer === String(correctIndex);
    }
    
    // 其他题型直接比较
    return userAnswer === correctAnswer;
  };
  
  // 如果用户没有作答
  if (!userAnswer) {
    return 'status-warning';
  }
  
  // 根据是否正确返回对应的类
  return isCorrect() ? 'status-correct' : 'status-wrong';
};
</script>

<style lang="scss" scoped>
.question-result-table {
  width: 100%;
  margin: 20px 0;
  
  .result-table {
    width: 100%;
    border-collapse: collapse;
    background-color: #f8f8f8;
    
    .table-header, .table-body {
      display: flex;
      width: 100%;
      
      .header-row, .body-row {
        display: flex;
        width: 100%;
        
        .header-cell, .body-cell {
          flex: 1;
          padding: 12px 8px;
          text-align: center;
          border: 1px solid #ebeef5;
          background-color: #f8f8f8;
          font-weight: bold;
          display: flex;
          align-items: center;
          justify-content: center;
          min-width: 40px;
          
          &.first-cell {
            min-width: 80px;
            background-color: #f0f0f0;
          }
        }
        
        .body-cell {
          background-color: #ffffff;
          
          &.first-cell {
            background-color: #f0f0f0;
          }
          
          .status-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #ffffff;
            
            &.status-correct {
              background-color: #67C23A;
            }
            
            &.status-wrong {
              background-color: #F56C6C;
            }
            
            &.status-warning {
              background-color: #E6A23C;
            }
            
            .el-icon {
              font-size: 14px;
            }
          }
        }
      }
    }
  }
}
</style> 