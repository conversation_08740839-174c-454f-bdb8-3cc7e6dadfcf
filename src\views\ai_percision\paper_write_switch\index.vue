<template>
    <div class="container" v-loading="writeState.loading">
      <div class="left">
        <div class="top-box">
            <div class="title-handle">
                <div class="title-box">
                    <img class="test-icon" src="@/assets/img/percision/title.png" />
                    <div class="test-title">{{ paperData.title }}</div>
                </div>
            </div>
        </div>
        <div class="test-content">
            <div v-for="(item, index) in allTest" :key="item.quesId" v-show="getShowState(item)" class="test-content-ques">
                <div class="squre"></div>
                <div class="test-tittle" style="display: flex;">
                    <div v-html="resetSty(item, index + 1)" />
                </div>
                <div class="test-body" v-html="resetOptions(item)" />
                <div v-if="item.ques.cate == 1 || item.ques.cate == 3" class="paper-content-ques">
                    <div v-if="item.ques.cate == 1">
                        <el-checkbox-group v-model="item.userJson" class="checkbox-style-checkbox">
                            <el-checkbox
                                v-for="(it,ind) in item.ques.options"
                                :key="ind"
                                @change="checkChange(ind,index)"
                                :label="ind"
                                size="large" border
                            >
                            {{ String.fromCharCode(65 + ind) }}
                            </el-checkbox>
                        </el-checkbox-group>
                    </div>
                    <div v-else>
                        <el-checkbox-group v-model="item.userJson" class="checkbox-style-checkbox">
                            <el-checkbox
                                v-for="(it,ind) in item.ques.options"
                                :key="ind"
                                :label="ind"
                                 @change="checkChangeM(item)"
                                size="large" border
                            >
                            {{ String.fromCharCode(65 + ind) }}
                            </el-checkbox>
                        </el-checkbox-group>
                    </div>
                </div>
                <div v-else class="paper-content-ques2">
                    <div v-if="writeState.showSubjective">
                        <div>
                            <div class="show-analyse">
                               
                                <el-switch @change="togAnswer(item,item.showAnalyse)" size="small" v-model="item.showAnalyse" /> <span>显示答案与解析</span>
                            </div>
                            <div v-show="item.showAnalyse" class="analyse">
                                <div class="flex-sty">
                                    <span>【知识点】</span>&nbsp;&nbsp;
                                    <div v-if="item.ques.pointVos != null" v-html="item.ques.pointVos[0].name" />
                                    <div v-else>--</div>
                                </div>
                                <div class="flex-sty">
                                    <span>【答案】</span>&nbsp;&nbsp;
                                    <div v-html="item.ques.displayAnswer" />
                                </div>
                                <div class="flex-sty">
                                    <span>【分析】</span>&nbsp;&nbsp;
                                    <div v-html="item.ques.analyse" />
                                </div>
                                <div class="flex-sty">
                                    <span>【解答】</span>&nbsp;&nbsp;
                                    <div v-html="item.ques.method" />
                                </div>
                                <div class="flex-sty">
                                    <span>【点评】</span>&nbsp;&nbsp;
                                    <div v-html="item.ques.discuss" />
                                </div>
                            </div>
                        </div>
                        <div class="answer-img-box">
                            <el-image
                                class="answer-img"
                                v-for="(it, ind) in item.userJson"
                                style="width: 10.8125rem; height: 10.8125rem;border-radius: .25rem;"
                                :src="it"
                                :zoom-rate="1.2"
                                :max-scale="7"
                                :min-scale="0.2"
                                :preview-src-list="item.userJson"
                                show-progress
                                :initial-index="ind"
                                fit="cover"
                            />
                        </div>
                        <div class="answers">
                            <div class="answer-box" @click="correcthandle(index, 1)" :class="item.userMark == 1?'green-box':''">
                                <div></div>正确
                            </div>
                            <div class="answer-box" @click="correcthandle(index, 2)" :class="item.userMark == 2?'yellow-box':''">
                                <div></div>半对
                            </div>
                            <div class="answer-box" @click="correcthandle(index, 0)" :class="item.userMark == 0?'red-box':''">
                                <div></div>错误
                            </div>
                        </div>
                    </div>
                    <div v-else class="padding2030">
                        <uploadAnswerImg :imgList="item.userJson" :index="index" @getImgList="handleImgList" />
                    </div>
                </div>
            </div>
        </div>
      </div>
      <div class="right">
        <div class="time-box">
            <div class="time-text">用时:</div>
            <div class="time-number"> {{ timeState.hours < 10 ? "0" + timeState.hours : timeState.hours }} </div> :
            <div class="time-number"> {{ timeState.minutes < 10 ? "0" + timeState.minutes : timeState.minutes }} </div> :
            <div class="time-number"> {{ timeState.seconds < 10 ? "0" + timeState.seconds : timeState.seconds }} </div>
        </div>
        <div class="test-number-box">
            <div class="test-number-item" v-for="(item, index) in allTest" v-show="getShowState(item)" :class="item.userJson && item.userJson.length > 0 ?'blue':''"> {{ index + 1 }} </div>
            <div v-show="writeState.isAllCorrect" class="icon-btn size285" @click="handleSubmit" v-loading="writeState.loading">
                <img src="@/assets/img/percision/submit.png" alt="">
                提交批改
            </div>
        </div>
      </div>
    </div>
    <el-dialog class="dialog-correct" v-model="writeState.showDialog" title="提交" align-center center>
        <p class="black-text">{{ writeState.subjectiveNum }}道主观题需要对照答案批改</p>
        <p class="grey-text">（同学们可以邀请家长一起完成哦）</p>
        <template #footer>
        <div class="dialog-footer">
            <div class="blue-btn" @click="toCorrect">去批改</div>
        </div>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import { watch, onMounted, reactive, ref, onUnmounted } from 'vue'
import uploadAnswerImg from '@/views/components/uploadAnswerImg/index.vue'
import { dataEncrypt, mergeObject, dataDecrypt } from "@/utils/secret"
import { useRouter, useRoute } from 'vue-router'
import { Action, ElMessage, ElMessageBox } from 'element-plus'
import { saveApi, practiceApi, getDetailsApi, reportTrainApi, createApi, saveUserJsonApi, createTrainToAtlasApi } from '@/api/training'
import { createNoteRedoTrainApi,saveNoteRedoTrainApi } from '@/api/note'
import { individuationTrainApi } from '@/api/analyse'
import { useUserStore } from "@/store/modules/user"
import { quesGetApi} from "@/api/video"
import { storeToRefs } from 'pinia'
import { Value } from 'sass'
const route = useRoute()
const router = useRouter()
const analysis :any = reactive({
    displayAnswer:'b',
    analyse:'',
    method:'',
    discuss:''
})
const timeState = reactive({
    hours: 0,
    minutes: 0,
    seconds: 0
})
const writeState = reactive({
    unWriteNum: [] as number[],
    showSubjective: false, // 是否只显示主观题
    showDialog: false,
    isAllCorrect: true, // 判断主观题是否全部批改
    subjectiveNum: 0,
    trainingId: '',
    loading: false
})
interface Ques {
    cate: number;
    cateName: string;
    content: string;
    displayAnswer: string;
    analyse: string;
    method: string;
    discuss: string;
    options: any[];
    pointVos: any[];
    userJson: any[];
    answers: any[];
}
class AData {
    quesId: string = ""
    cateName: string = ""
    content: string = ""
    userMark: number | null = null
    options: any[] = []
    showAnalyse: boolean = false;
    userJson: any[] = []
    answer: any[] = []
    ques: Ques = { // 添加 ques 属性
        cate: 0,
        cateName: "",
        content: "",
        analyse: "",
        discuss: "",
        method: "",
        displayAnswer: "",
        options: [],
        pointVos: [],
        userJson: [],
        answers: []
    };
}
let timer :  NodeJS.Timeout | null = null
const allTest = ref([] as AData[])
const userStore = useUserStore()
const { subjectObj } = storeToRefs(userStore)
const queryData = route.query.data ? dataDecrypt(route.query.data) : {}
let paperData = reactive({
    title: '',
    id: '',
    collectStatus: false,
    groups: [] as any[]
})
watch(() => timeState.seconds, () => {
    if(timeState.seconds == 60) {
        timeState.minutes ++
        timeState.seconds = 0
    }
    if(timeState.minutes == 60) {
        timeState.hours ++
        timeState.minutes = 0
    }

})
onMounted(() => {
    window.customGoBack = customGoBack
    localStorage.setItem('isPopSaveDialog', '1')
    console.log(queryData.contentType,"paper_write_switchMpaper_write_switchM")
    createTrain()

    timer = setInterval(() => {
        timeState.seconds ++
    }, 1000)

})
onUnmounted(() => {
    if (timer !== null) { // 添加类型安全检查
        clearInterval(timer)
    }
        // 清除自定义返回方法
    if (window.customGoBack) {
      delete window.customGoBack
    }
})

  // 自定义返回方法
  const customGoBack = () => {
      router.go(-1)
  }


  //显示答案
const togAnswer = async (item:any,isShow:any) => { 
    if(isShow){
        // 如果已经有完整的题目信息，直接显示，无需重复请求
        if (item.ques.analyse && item.ques.method && item.ques.discuss) {
            return
        }   
        try {
            // 添加加载状态，防止重复点击
            if (item.loading) return
            item.loading = true
            
            const response = await quesGetApi({id: item.ques.quesId}) as any
            
            if (response.code === 200 && response.data) {
                // 使用 Object.assign 来安全地合并数据，保留原有属性
                Object.assign(item.ques, response.data)
                
                // 确保必要的属性存在
                if (!item.ques.pointVos) {
                    item.ques.pointVos = []
                }
                if (!item.ques.options) {
                    item.ques.options = []
                }
                if (!item.ques.answers) {
                    item.ques.answers = []
                }
                console.log('题目详细信息已更新:', item.ques)
            } else {
                console.error('获取题目详细信息失败:', response)
                // 如果获取失败，关闭显示开关
                item.showAnalyse = false
                // 可以添加用户提示
            }
        } catch (error) {
            console.error('获取题目详细信息时发生错误:', error)
            // 发生错误时关闭显示开关
            item.showAnalyse = false
            // 可以添加用户提示
        } finally {
            // 清除加载状态
            item.loading = false
        }
    }

}

const createTrain = () => {
    // pageSource 1是真题试卷，2是文科AI精准学去测评, 3是理科知识图谱单元测试, 4是知识点扫雷，5是闯关, 6是五步学习去订正, 7是学生学情错题去订正, 8是五步学习举一反三, 9是Ai错题本举一反三, 10是错题本去订正
    let api = practiceApi
    let formdata
    if (queryData.pageSource == '1') {
        formdata = new FormData()
        // formdata.append("reportId", queryData.id)
        // formdata.append("isCreate", "1")
        // let newSource = '110'
        // switch (queryData.source) {
        //     case '1':
        //         newSource = '110'
        //         break;
        //     case '2':
        //         newSource = '111'
        //         break;
        //     case '3':
        //         newSource = '112'
        //         break;
        //     case '4':
        //         newSource = '113'
        //         break;
        //     case '5':
        //         newSource = '114'
        //         break;
        // }
        // formdata.append("source", newSource)
        // formdata.append("bookId", subjectObj.value.bookId)
         // api = createTrainToAtlasApi
        formdata.append("isCreate", '0')
        formdata.append("reportId", queryData.id)
        formdata.append("source", queryData.source)
       
        api = reportTrainApi
    } else if (queryData.pageSource == '4' || queryData.pageSource == '5') {
        formdata = new FormData()
        if (queryData.chapterTrainType == '7') {
            formdata.append("individuationId", queryData.individuationId)
            api = individuationTrainApi
        } else {
            formdata.append("chapterId", queryData.chapterId)
            formdata.append("chapterTrainType", queryData.chapterTrainType)
            api = practiceApi
        }
    } else if (queryData.pageSource == '6' || queryData.pageSource == '7' || queryData.pageSource == '10') {
        formdata = {
            subject: subjectObj.value.id,
            chapterId: queryData.chapterId,
            source: 0,
            importance: 0,
            mastery: 0,
            reason: 0,
            type: 1,
            wrongCount: 0,
        }
        if(queryData.pageSource == '6' || queryData.pageSource == '10'){
          // 错题本列表-去订正
          formdata.noteIds = queryData.noteId
        }
        api = createNoteRedoTrainApi
    } else if (queryData.pageSource == '8' || queryData.pageSource == '9') {
        formdata = new FormData()
        formdata.append("noteId", queryData.noteId)
        // formdata = {
        //     noteId: queryData.noteId,
        // }
        api = createApi
    }
    api(formdata).then((res: any) => {
        if (res.data) {
            writeState.trainingId = res.data
            // 获取详情
            getDetails()
        }
    }).catch((error) => {
    })
}
const getDetails = () => {
    writeState.loading = true
    getDetailsApi({trainingId: writeState.trainingId}).then((res1: any) => {
        if (res1.code == 200) {
            formatMillisecondsToHMS(res1.data?.trainTime ? Number(res1.data.trainTime) : 0)
            res1.data.items.forEach((item) => {
                item.showAnalyse = false
                if (item.userJson) {
                    if (item.ques.cate != 1 && item.ques.cate != 3) {
                        const urlArr = item.userJson.slice(1, -1).split(',')
                        let arr = [] as any[]
                        urlArr.map((item, index) => {
                            arr.push({name: 'img' + index, url: item})
                        })
                        item.userJson = arr
                    } else {
                        item.userJson = JSON.parse(item.userJson)
                    }
                }
                item.ques.userJson = []
                if (item.userJson == null) {
                    item.userJson = []
                }
            })
            paperData = res1.data
            allTest.value = res1.data.items
            console.log(allTest.value)
        }
        writeState.loading = false

    }).catch((error) => {
        writeState.loading = false
    })
}
function formatMillisecondsToHMS(milliseconds) {
  const totalSeconds = Math.floor(milliseconds / 1000);
  timeState.hours = Math.floor(totalSeconds / 3600);
  timeState.minutes = Math.floor((totalSeconds % 3600) / 60);
  timeState.seconds = totalSeconds % 60;
}
const getShowState = (data: any) => {
    let isShow = true
    if (writeState.showSubjective) {
        if (data.ques.cate == 1 || data.ques.cate == 3) {
            isShow = false
        } else {
            isShow = true
        }
    } else {
        isShow = true
    }
    return isShow

}
const checkChange = (val: any, index: number) => {
    // 验证 index 是否在有效范围内
    if (index < 0 || index >= allTest.value.length) {
        console.error(`Invalid index: ${index}`);
        return;
    }

    // 确保 allTest.value[index] 存在且 userJson 已初始化
    const currentItem = allTest.value[index];
    if (!currentItem) {
        console.error(`Item at index ${index} is undefined`);
        return;
    }

    // 更新 userJson
    currentItem.userJson = [val];
    saveUserJson(currentItem)
}
const handleImgList = (index: number, imgList: any) => {
        // 验证 index 是否在有效范围内
        if (index < 0 || index >= allTest.value.length) {
        console.error(`Invalid index: ${index}`);
        return;
    }

    // 确保 allTest.value[index] 存在且 userJson 已初始化
    const currentItem = allTest.value[index];
    if (!currentItem) {
        console.error(`Item at index ${index} is undefined`);
        return;
    }

    // 更新 userJson
    currentItem.userJson = imgList;
    // allTest.value[index].userJson = imgList
    saveUserJson(currentItem)
}
//过滤修改题干标签内容
const resetSty = function (testItem: any, sort?: number) {
  const tittle = "（" + testItem.ques.cateName + "）" +sort + "." + filterContent(testItem.ques.content)
  return tittle
}
//过滤掉填空题等div标签的contenteditable属性
const filterContent = (contect: string) => {
  let highlightedResult = ""
  highlightedResult = contect.replaceAll('contenteditable="true"', " ")
  highlightedResult = highlightedResult.replaceAll("contenteditable='true'", " ")
  return highlightedResult
}
//过滤修改选项内容
const resetOptions = function (testItem: any) {
    let optionHtml = ""
    if (!testItem.ques.options) return
    testItem.ques.options.map((item: any, index: number) => {
        optionHtml += `<div class="answer-item"> ${String.fromCharCode(65 + index)}. <div style="display:inline-table;max-width:39.8125rem">${item}</div></div>`
    })
    return optionHtml
}
const handleSubmit = () => {
    if (writeState.showSubjective) {
        submit()
    } else {
        writeState.unWriteNum =  []
        writeState.subjectiveNum =  0
        allTest.value.map((item: any, index: number) => {
            if (item.userJson == null || (item.userJson && item.userJson.length == 0)) {
                writeState.unWriteNum.push(index + 1)
            }
            if(item.ques.cate != 1 && item.ques.cate != 3) {
                writeState.subjectiveNum++
            }
        })
        if (writeState.unWriteNum.length > 0) {
            ElMessageBox.confirm(`第${writeState.unWriteNum.toString().replaceAll(',', '、 ')}题未作答，您确定要提交吗？`, '退出', {
                  confirmButtonText: '继续答题',
                  cancelButtonText: '确定提交',
                  distinguishCancelAndClose: true,
                  center: true
              }).then(() => {
              }).catch((action: Action) => {
                console.log(action)
                // 区分取消还是关闭
                if (action == 'cancel') {
                    if (writeState.subjectiveNum > 0) {
                        writeState.showDialog = true
                    } else {
                        submit()
                    }
                }
                if (timer !== null) { // 添加类型安全检查
                    clearInterval(timer)
                }
              })
        } else {
            if (timer !== null) { // 添加类型安全检查
                clearInterval(timer)
            }
            if (writeState.subjectiveNum > 0) {
                writeState.showDialog = true
            } else {
                submit()
            }
        }
    }

}

// 将时分秒转换为毫秒时间戳
const convertTimeToMilliseconds = () => {
    const totalSeconds = timeState.hours * 3600 + timeState.minutes * 60 + timeState.seconds
    return totalSeconds * 1000 // 转换为毫秒
}

const toCorrect = () => {
    // 把答题图片转化为规定形式
    allTest.value.forEach((item: any) => {
        if (item.ques.cate != 1 && item.ques.cate != 3) {
            const UrlArr = JSON.parse(JSON.stringify(item.userJson)) || []
            item.userJson = UrlArr.map((item: any) => { return item.url }) || []
            item.images = UrlArr.map((item: any) => { return item.url }) || []
        }
    })
    writeState.showDialog = false
    writeState.showSubjective = true
    writeState.isAllCorrect = false
}
const correcthandle = (index: number, userMark: number) => {
    // 验证 index 是否在有效范围内
    if (index < 0 || index >= allTest.value.length) {
        console.error(`Invalid index: ${index}`);
        return;
    }

    // 确保 allTest.value[index] 存在且 userJson 已初始化
    const currentItem = allTest.value[index];
    if (!currentItem) {
        console.error(`Item at index ${index} is undefined`);
        return;
    }

    // 更新 userJson
    currentItem.userMark = userMark;
    // 判断是否已全部批改
    writeState.isAllCorrect = true
    allTest.value.map((item: any) => {
        if (item.ques.cate != 1 && item.ques.cate != 3) {
            if (item.userMark == null) {
                writeState.isAllCorrect = false
            }
        }
    })
}
// 批改选择题
const checkSelect = () => {
    return new Promise((resolve, reject) => {
        try {
            let arr= [] as any[]
            allTest.value.forEach((item: any, index: number) => {
                // 只处理单选题(cate=1)和多选题(cate=3)
                if (item.ques.cate == 1 || item.ques.cate == 3) {
                    // 用户未作答的情况
                    if (item.userJson == null || (item.userJson && item.userJson.length == 0)) {
                        item.userMark = 0; // 未作答记为错误
                    }
                    // 单选题批改逻辑
                    else if (item.ques.cate == 1) {
                        const userAnswers = item.userJson.toString();
                        const correctAnswers = item.ques.answers.toString();
                        // 单选题直接比较答案字符串是否相等
                        item.userMark = (userAnswers == correctAnswers ? 1 : 0);
                    }
                    // 多选题批改逻辑
                    else if (item.ques.cate == 3) {
                        // 将答案字符串转换为数组并排序(假设答案格式如"A,B,C")
                        const userAnswers = item.userJson.sort();
                        const correctAnswers = item.ques.answers?.sort();

                        // 完全正确
                        if (userAnswers.join(',') === correctAnswers.join(',')) {
                            item.userMark = 1;
                        }
                        // 部分正确(用户答案是正确答案的子集)
                        else if (userAnswers.every(ans => correctAnswers.includes(ans))) {
                            item.userMark = 2; // 半对
                        }
                        // 错误
                        else {
                            item.userMark = 0;
                        }
                    }
                }
            });
            resolve(true); // 批改完成
        } catch (error) {
            reject(error); // 出错时reject
        }
    });
}
// const countScore = () => {
//     return new Promise((resolve, reject) => {
//         try {
//             let rate = 0
//             allTest.value.forEach((item: any, index: number) => {
//                 if (item.userMark = 1) {
//                     rate += 1
//                 } else if (item.userMark = 2) {
//                     rate += 0.5
//                 }
//             })
//         } catch (error) {
//             reject(error); // 出错时reject
//         }
//     })
// }
// 多选框变化
const checkChangeM = (data: any) => {
    console.log(data)
    saveUserJson(data)
}
const saveUserJson = (data: any) => {
    let userJsons = data.userJson
    if (data.ques.cate != 1 && data.ques.cate != 3) {
        userJsons = data.userJson.map((item: any) => { return item.url })
    }
    const params = {
        trainingItemId: data.trainingItemId,
        userJson: userJsons,
        cate: data.ques.cate,
        userMark: data.userMark,
        trainTime: convertTimeToMilliseconds() ,
        trainItemTime: 1000
    }
    saveUserJsonApi(params)
    .then((res: any) => {})
    .catch(() => {
        ElMessage({
            message: '保存失败！',
            type: 'error'
        })
    })
}
const submit = async() => {
    writeState.loading = true
    let itemsArr = [] as any[]
    await checkSelect()
    // await countScore()
    allTest.value.map((item: any, index: number) => {
        itemsArr.push({ trainingItemId: item.trainingItemId, userJson: (item.ques.cate == 1 || item.ques.cate == 3)?item.userJson: [], images: (item.images && item.images.length != 0)?item.images.toString():"", cate: item.ques.cate, userMark: item.userMark, trainItemTime: 0 })
    })
    let rate = 0
    allTest.value.forEach((item: any, index: number) => {
        if (item.userMark == 1) {
            rate += 1
        } else if (item.userMark == 2) {
            rate += 0.5
        }
    })
    const params = {
        trainingId: writeState.trainingId,
        // trainTime: timeState.seconds * 1000,
        trainTime: convertTimeToMilliseconds(),
        status: 2,
        items: itemsArr,
        correctRate: rate / allTest.value.length * 100,
        // reviseCount: 1,
        // score: 20,
        learnReportType: 1
    }
  if (queryData.pageSource == '6' || queryData.pageSource == '10') {
    //错题本-去订正
      saveNoteRedoTrainApi(params).then((res: any) => {
        ElMessage({
            message: '提交成功',
            type: 'success'
        })
        writeState.loading = false
        toDetails()
    })
    .catch(() => {
        writeState.loading = false
    })
  } else {
      saveApi(params).then((res: any) => {
        ElMessage({
            message: '提交成功',
            type: 'success'
        })
        writeState.loading = false
        toDetails()
    })
    .catch(() => {
        writeState.loading = false
    })
 }

}
const toDetails = async () => {
    // pageSource 1是真题试卷，2是文科AI精准学去测评, 3是理科知识图谱单元测试, 4是知识点扫雷，5是闯关, 6是五步学习去订正, 7是学生学情错题去订正, 8是五步学习举一反三, 9是Ai错题本举一反三, 10是错题本去订正
    let url = ""
    if (queryData.pageSource == '1') {
        url = '/true_paper/true_paper_page/paper_analysisT'
    } else if (queryData.pageSource == '4') {
        url = '/ai_percision/minesweeper/training_report_wenM'
    } else if (queryData.pageSource == '5') {
        url = '/ai_percision/to_practice/training_report_wenP'
    } else if (queryData.pageSource == '6' || queryData.pageSource == '8') {
        url = '/ai_percision/knowledge_graph_detail/training_report_wenC'
    } else if (queryData.pageSource == '7') {
        url = '/academic_report/academic_report_page/training_report_wenA'
    } else if (queryData.pageSource == '9' || queryData.pageSource == '10') {
        url = '/note/training_report_wenAJ'
    }
    localStorage.setItem('isPopSaveDialog', '0')
    if (queryData.pageSource == '9' || queryData.pageSource == '10') {
      router.replace({
        path: url,
        query: {
            data: dataEncrypt({
                trainingId: writeState.trainingId,
                chapterTrainType: queryData.chapterTrainType,
                pageSource: queryData.pageSource,
                contentType:queryData.contentType
            })
        }
      })
    } else {
      router.push({
          path: url,
          query: {
              data: dataEncrypt({
                  trainingId: writeState.trainingId,
                  chapterTrainType: queryData.chapterTrainType,
                  pageSource: queryData.pageSource,
                  contentType:queryData.contentType||'paper_write_switch'
              })
          }
      })
    }

}
</script>
<style lang="scss" scoped>
.container {
    display: flex;
    .left {
        width: 60.3125rem;
        .top-box {
            padding: 1.25rem;
            background: #ffffff;
            .title-handle {
                display: flex;
                justify-content: space-between;
                .title-box {
                    display: flex;
                    align-items: center;
                    .test-icon {
                        width: 1.125rem;
                        height: 1.125rem;
                    }
                    .test-title {
                        color: #2a2b2a;
                        font-size: 1rem;
                        font-weight: 700;
                        margin-left: .5rem;
                        margin-right: .625rem;
                    }
                    .size84 {
                        width: 5.25rem;
                        height: 1.875rem;
                        font-size: .875rem;
                        img {
                            width: .75rem;
                            height: .75rem;
                        }
                    }
                }
                .btn {
                    width: 7.375rem;
                    height: 1.875rem;
                    line-height: 1.875rem;
                    text-align: center;
                    font-size: .875rem;
                    border-radius: .25rem;
                    border: .0625rem solid #009c7f;
                    background: #ffffff;
                    cursor: pointer;
                    color: #009c7f;
                }
            }
            .title-data {
                margin-top: .625rem;
                margin-bottom: 1rem;
                &-item {
                    display: inline-block;
                    border-radius: .875rem;
                    padding: .375rem .75rem;
                    background: #fef8e9;
                    color: #ef9d19;
                    font-size: .75rem;
                    font-weight: 400;
                    margin-right: .625rem;
                }
            }
        }
        .test-content {
            height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height) - 7.5rem);
            box-sizing: border-box;
            overflow-y: auto;
            padding-top: .625rem;
            .test-content-ques {
                background: #ffffff;
                width: 100%;
                box-sizing: border-box;
                padding: 1.25rem 0 0 0 ;
                margin-bottom: .625rem;
                position: relative;
                .squre {
                    width: .875rem;
                    height: 1rem;
                    border-radius: 0 .375rem .375rem 0;
                    background: #5a85ec;
                    position: absolute;
                    top: 1.625rem;
                    left: 0;
                }
                .test-tittle,.test-body {
                    padding: 0 1.875rem;
                }
            }
        }
    }
    .right {
        padding: 1.125rem 0;
        width: 20.3125rem;
        margin-left: .625rem;
        background: #ffffff;
        box-sizing: border-box;
        height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height));
        .time-box {
            display: flex;
            align-items: center;
            color: #2a2b2a;
            font-size: 1.875rem;
            font-weight: 700;
            padding-left: .625rem;
            padding-bottom: 1.25rem;
            border-bottom: .0625rem dashed #eaeaea;
            .time-text {
                font-size: 1rem;
                font-weight: 700;
            }
            .time-number {
                width: 3.75rem;
                height: 3.75rem;
                line-height: 3.75rem;
                text-align: center;
                border-radius: .25rem;
                border: .0625rem solid #eaeaea;
                background: #f5f5f5;
                margin: 0 .625rem;
            }
        }
        .test-number-box {
            height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height) - 8.125rem);
            box-sizing: border-box;
            padding: 1.25rem .625rem 1.25rem 0;
            .test-number-item {
                display: inline-block;
                margin-left: .625rem;
                margin-bottom: .625rem;
                border-radius: .25rem;
                width: 2.5rem;
                height: 2.5rem;
                line-height: 2.5rem;
                text-align: center;
                color: #2a2b2a;
                font-size: 1rem;
                font-weight: 400;
                border: .0625rem solid #eaeaea;
                background: #ffffff;
                box-sizing: border-box;
                &:hover {
                    background: #f5f5f5;
                }
            }
            .blue {
                background: #5a85ec;
                color: #ffffff;
            }
            .size285 {
                width: 17.8125rem;
                height: 2.75rem;
                font-size: 1rem;
                font-weight: 700;
                margin-left: .625rem;
                margin-top: 1.25rem;
                img {
                    width: 1rem;
                    height: 1rem;
                }
            }
        }
    }
}
.icon-btn {
    border-radius: .25rem;
    background: #00c9a3;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    img {
        margin-right: .3125rem;
    }
}
.answer-img-box {
    padding-left: 1.875rem;
    padding-top: 1.25rem;
    .answer-img {
        width: 10.8125rem;
        height: 10.8125rem;
        border-radius: .25rem;
        margin-right: .625rem;
    }
}
.show-analyse {
    width: 100%;
    background: #fef8e9;
    padding-left: 1.875rem;
    height: 2.1875rem;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    color: #666666;
    font-size: .75rem;
    font-weight: 400;
    margin-top: 1.25rem;
    span {
        margin-left: .375rem;
    }
}
.analyse {
    padding: .625rem 1.875rem;
    letter-spacing: .125rem;
    background: #fef8e9;
    div {
      margin-bottom: .625rem;
    }
}
.flex-sty {
  display: flex;
  font-size: .875rem;
  align-items: baseline;
  div {
    max-width: 52.375rem;
    line-height: 1.0625rem;
  }
  span {
    text-wrap: nowrap;
    font-weight: 700;
    letter-spacing: normal;
  }
}
.green-box {
    border: .0625rem solid #00c9a3!important;
    background: #e5f9f6!important;
    div {
        background-image: url(@/assets/img/percision/right-check.png);
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }
}
.red-box {
    border: .0625rem solid #dd2a2a!important;
    background: #fce9e9!important;
    div {
        background-image: url(@/assets/img/percision/wrong-check.png);
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }
}
.yellow-box {
    border: .0625rem solid #f1be21!important;
    background: #fef8e8!important;
    div {
        background-image: url(@/assets/img/percision/harf-right.png);
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }
}
.padding2030 {
    padding: 1.25rem 1.875rem;
}
.answers {
    display: flex;
    margin-top: 1.25rem;
    .answer-box {
        width: 6.25rem;
        height: 3.125rem;
        border-radius: .25rem;
        cursor: pointer;
        border: .0625rem solid #dddddd;
        background: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1.25rem;
        &:first-child {
            margin-left: 1.875rem;
        }
        div {
            border: .0625rem solid #999999;
            width: 1rem;
            height: 1rem;
            border-radius: .125rem;
            margin-right: .625rem;
        }
    }
}
:deep(.el-checkbox-group) {
    .is-checked {
        .el-checkbox__inner {
            background: #5a85ec;
            border: .0625rem solid #5a85ec;
        }
        .el-checkbox__label {
            color: #5a85ec;
        }
    }
    .el-checkbox__inner {
        &:hover {
            border: .0625rem solid #5a85ec;
        }
    }
}
.paper-content-ques {
    margin-top: 1.25rem;
    border-top: .0625rem dashed #EAEAEA;
    padding: 1.25rem 1.875rem;
}
.paper-content-ques2 {
    margin-top: 1.25rem;
    border-top: .0625rem dashed #EAEAEA;
}
.black-text {
    color: #2a2b2a;
    font-size: 1rem;
    font-weight: 400;
    text-align: center;
}
.grey-text {
    color: #999999;
    font-size: .875rem;
    font-weight: 400;
    text-align: center;
}

.dialog-footer {
    margin-top: 9.375rem;
    display: flex;
    justify-content: center;
}
.blue-btn {
    width: 7.625rem;
    height: 2.375rem;
    line-height: 2.375rem;
    cursor: pointer;
    border-radius: 1.1875rem;
    background: #00c9a3;
    color: #ffffff;
    text-align: center;
}
</style>
<style lang="scss">
.answer-item:not(:last-child) {
    margin-bottom: 1.875rem;
}
.dialog-correct {
    width: 34.875rem!important;
    border-radius: 1.25rem;
    box-sizing: border-box;
}
</style>
