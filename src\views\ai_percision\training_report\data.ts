import { reactive } from "vue";

export const options = reactive({
    title: {
      text: '题目难度',
      left: 'center',
      top: 'center',
      textStyle: {
        color: '#2A2B2A',
        fontSize: 14,
        fontWeight: 'bold' as any
      }
    },
    color: [
        '#85BAE6',
        '#22DBB8',
        '#3E99E6',
        '#F3AE3E',
        '#DA5353'
      ],
    tooltip: {
      show: false
    },
    legend: {
      bottom: '5%',
      show: true,
      selectedMode: false,
      icon: 'roundRect',
      itemWidth: 11,  // 图标宽度
      itemHeight: 11,  // 图标高度
      left: 'center',
      itemGap: 30,
      textStyle: {
        lineHeight: 11,  // 文字行高（与图标高度一致）
      }
    },
    series: [
      {
        name: '',
        type: 'pie',
        minShowLabelAngle: 1,
        radius: ['35%', '55%'],
        avoidLabelOverlap: true,
        emphasis: { disabled: true },
        label: {
          show: true,
          color: '#333',
          fontSize: 12,
          formatter: function(params) {
            return params.value > 0 ? `${params.name}: ${params.value}%` : '';
          },
          rich: {
            b: { color: '#666' },
            c: { color: '#666' }
          }
        },
        labelLine: {
          show: function(params) {
            return params
          },
          lineStyle: {
            color: '#aaa',
            width: 1,
            type: 'solid' // 实线
          },
          symbolSize: [8, 8]
        },
        data: [] as any[]
      }
    ] as any[]
})