<template>
  <div class="training-page">
    <div class="left-sidebar" v-loading="loading">
      <knowledgeTree
        :selected="chapterId"
        :iswen="true"
        :options="options"
        :showCurrentTaskIndicator="!!routeHasChapterId"
        currentTaskImageUrl="@/assets/img/synchronous/task-badge.png"
        @setChapterId="setChapterId"
        :defaultExpandAll="true"
      />
      <div class="sidebar-title">
        选择章节
        <span class="title-decoration"></span>
      </div>
    </div>

    <div class="main-content" v-loading="loading2">
      <div class="content-head">
        <div class="head-body">
          <img src="@/assets/img/percision/training/textbook.png" alt="" class="textbook" />
          <div class="head-title">
            当前教材：{{ bookVersionName }}
          </div>
          <div @click="onModify" class="head-switch">切换教材</div>
        </div>

        <img @click="onMark" src="@/assets/img/percision/training/superficiality.png" alt="" class="superficiality" />
        <div class="catalogue">
          <span>{{ chapterPathText }}</span>
          <img src="@/assets/img/percision/training/dsj.png" alt="">
        </div>
            
      </div>

      <div class="content-tip">
        <div class="tip-content">
          <img src="@/assets/img/percision/training/mty.png" alt="" class="tip-avatar" />
          <div class="tip-text">本节为老师布置的任务，请在规定时间内完成。<span style="color:#DD2A2A">截止时间：2025/05/15</span></div>
        </div>
        <img src="@/assets/img/percision/training/tip_bg.png" class="tip-bg" alt="" />
      </div>
      <!-- 知识点训练列表 -->
      <div class="knowledge-training-list" v-if="pageStatus">
        <div class="lesson-section" v-if="lessonData.length > 0">

          <div class="knowledge-info">
            <div class="knowledge-box">
              <!-- 将知识点每两个分为一课 -->
              <div 
                class="lesson-container" 
                v-for="(_, lessonIndex) in Math.ceil(lessonData.length / 2)" 
                :key="'lesson-' + lessonIndex"
              >
                <div class="lesson-header">
                  <div class="lesson-title">第{{ lessonIndex + 1 }}课</div>
                </div>
                
                <div class="lesson-content">
                  <!-- 遍历当前课的两个知识点 -->
                  <div 
                    class="knowledge-item" 
                    v-for="knowledge in lessonData.slice(lessonIndex * 2, lessonIndex * 2 + 2)" 
                    :key="knowledge.id"
                  >
                    <div class="knowledge-name" @click="showKnowledgeDetail(knowledge)">
                      <span class="knowledge-title">{{ knowledge.name }}</span>
                    </div>
                    <div class="progress-area">
                      <div class="hexagon-group">
                        <!-- 基础训练六边形 -->
                        <div class="hexagon-wrapper">
                          <div class="hexagon-bg" :class="getHexagonBgClass(knowledge.correctRate || 0, 'basic')">
                            <div class="hexagon-content">

                              <img v-if="(knowledge.correctRate || 0) >= 100"
                                   :src="getSmallMedalIcon(1)"
                                   class="medal-crown" />
                              <div class="percentage-text">{{ knowledge.correctRate || 0 }}%</div>
                            </div>
                          </div>
                        </div>

                        <el-icon><CaretRight color="#EAEAEA" /></el-icon>          
                        <!-- 进阶训练六边形 -->
                        <div class="hexagon-wrapper">
                          <div class="hexagon-bg" :class="getHexagonBgClass(knowledge.correctRate || 0, 'advanced')">
                            <div class="hexagon-content">
                              <div class="percentage-text">{{ knowledge.correctRate || 0 }}%</div>
                              <img v-if="(knowledge.correctRate || 0) >= 100"
                                   :src="getSmallMedalIcon(2)"
                                   class="medal-crown" />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="action-area">
                      <div class="action-btn study" @click="toPractice(knowledge)">
                        <img src="@/assets/img/percision/training/play.png" class="action-icon" />
                        去学习
                      </div>
                      <div class="action-btn practice" @click="toPractice(knowledge)">
                        <img src="@/assets/img/percision/training/practice.png" class="action-icon"  />
                        练习记录
                      </div>
                    </div>
                    
                    <!-- 知识点状态指示器 -->
                    <!-- <div class="knowledge-status" :class="getKnowledgeStatusClass(knowledge)">
                      {{ getKnowledgeStatusText(knowledge) }}
                    </div> -->
                  </div>
                </div>
                
                <!-- 挑战按钮 -->
                <div 
                  class="challenge-btn"
                  @click="handleChallengeClick({id: lessonIndex + 1, knowledgePoints: lessonData.slice(lessonIndex * 2, lessonIndex * 2 + 2)})"
                >
                  {{ getChallengeButtonText({knowledgePoints: lessonData.slice(lessonIndex * 2, lessonIndex * 2 + 2)}) }}
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div v-else class="empty-state">
          <div class="empty-message">暂无知识点数据</div>
        </div>
      </div>
      <!-- 单元测试 -->
      <div v-else v-loading="loading">
          <div class="test-box">
            <div class="test-wrap" v-for="item in testList">
              <div class="test-box-item">
                <div class="test-box-item-img">
                  <span class="red-text" v-if="item.score&&item.score!='0'">{{item.score}}分</span>
                </div>
                <div class="test-box-item-info">
                  <div class="test-box-item-info-title">
                    {{item.title}}
                  </div>
                  <div class="test-box-item-info-data">
                    <div>更新时间：{{item.reportDate	}}</div>
                    <div>浏览：{{ item.viewCount }}</div>
                  </div>
                </div>
                <div class="test-box-item-btn">
                  <div class="test-box-item-btn-it btn" @click="handleDownload(item)">
                    <img src="@/assets/img/percision/download.png" alt=""> 下载
                  </div>
                  <div class="test-box-item-btn-it blue-text" @click="testDetail(item)">
                    查看详情>
                  </div>
                </div>
              </div>
              <div class="hui-line"></div>
            </div>
          </div>
          <div class="pagination-box">
            <Pagination
                :total="pageData.total"
                :current="pageData.current"
                @currentSizeChange="currentSizeChange"
                @pageClick="pageClick"/>
          </div>
        </div>
      
      <!-- 知识点详情弹窗 -->
      <el-dialog
        v-model="knowledgeDetailVisible"
        :title="selectedKnowledge?.name || '知识点详情'"
        width="60%"
        :before-close="handleDetailClose"
      >
        <div class="knowledge-detail-content" v-if="selectedKnowledge">
          <div v-html="selectedKnowledge.desc"></div>
        </div>
      </el-dialog>
    </div>
    <div v-if="challengePop" class="elevate-overlay">
      <div class="elevate-ct">
        <div class="close-btn" @click="challengePop = false">
          <img src="@/assets/img/percision/training/hscc.png" alt="">
        </div>
        <div class="top-title">本次检测{{ knowledgeList?.length || 0 }}个知识点</div>
        <div class="block-ct">
          <div 
            class="book-list" 
            v-for="(point, index) in knowledgeList" 
            :key="index"
          >
          <!-- {{ knowledgeList }} -->
            <img src="@/assets/img/percision/training/bookzsd.png" alt="">
            <div class="book-name">{{ index + 1 }}.{{ point.pointName }}</div>
            <div class="book-tl">题量<span class="num">{{ point.quesCount || 3 }}</span></div>
            <div class="book-tl">难度<span class="num">{{ point.degree }}</span></div>
          </div>
          <div class="prompt"> 共 <span>{{ knowledgeOll.quesCount }}</span>道题，要求 <span>{{ knowledgeOll.times }}</span> 分钟内完成</div>
        </div>
        <div class="challenge-fq">向<img src="@/assets/img/percision/training/qingtong.png" alt="">发起挑战吧，正确率≥90%即可过关！</div>
        <div class="book-challenge" @click="onChallenge">开始挑战</div>
      </div>
    </div>
  </div>
    <!-- 下载试卷 -->
  <downloadTrestDialog v-if="dialogVisible" ref="downloadTrestDialogRef" :paper-detail="dowmloadData" />
</template>

<script lang="ts" setup>
import { useRouter ,useRoute} from 'vue-router'
import knowledgeTree from "@/views/components/knowledgeTree/trainingTree.vue"
import { getBookChapterListApi, getMasteryApi, getBookChapterListsApi, getPointCategoryApi,getChapterListApi,getpointListApi } from "@/api/book"
import {  addTrainingApi, trainingInfoApi} from "@/api/precise"
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'
import { getChapterReportListApi } from "@/api/report"
import { useUserStore } from "@/store/modules/user"
import { dataEncrypt, dataDecrypt } from "@/utils/secret"
import { storeToRefs } from 'pinia'
import { CaretRight } from '@element-plus/icons-vue'
const userStore = useUserStore()

const { subjectObj, learnNow } = storeToRefs(userStore)
const learnUsers = JSON.parse(localStorage.learnUsers || "[]") || []
const router = useRouter()
const route = useRoute()
const query = reactive<any>(route.query)
const loading = ref(false)
const loading2 = ref(false)
const pageStatus = ref(true)
const challengePop = ref(false)
const chapterName = ref("")
const trainingId = ref()
import downloadTrestDialog from "@/components/TruePaperDownload/index.vue"

// 存储选中的子级ID数组
const selectedChildIds = ref<string[]>([])

const chapterId = ref("")
const routeHasChapterId = ref(false) // 标记是否从路由获取了章节ID
const isTargetChapterId = ref()
const chapterPath = ref<any>([]) // 存储当前章节路径
const forceUpdatePath = ref(0) // 强制更新路径的计数器
const knowledgeList = ref<any>([]) 
const knowledgeOll = reactive({
  times: 120, // 时间，单位：分钟（从API返回的秒数转换而来）
  quesCount: 1 // 总题目数量
})

// 监听chapterId变化，用于调试
watch(() => chapterId.value, (newValue, oldValue) => {
  console.log("chapterId变化:", oldValue, "->", newValue)
  if (newValue) {
    console.log("传递给knowledgeTree的selected值:", newValue)
    // 强制刷新路径计算
    forceUpdatePath.value++
  }
}, { immediate: true })

// 监听chapterPath变化
watch(() => chapterPath.value, (newPath, oldPath) => {
  console.log("chapterPath变化:")
  console.log("旧路径:", oldPath?.map(c => c.name || c.chapterName) || [])
  console.log("新路径:", newPath?.map(c => c.name || c.chapterName) || [])
  
  // 强制刷新路径计算
  forceUpdatePath.value++
}, { immediate: true, deep: true })

const getTrid = () =>{
  let uniqueArr;
    // 知识点标熟
  addTrainingApi({
    bookId: query.bookId,
    pointIds: uniqueArr,
    type: 1,
    subject:query.subject,
    chapterId:chapterId.value


  }).then((res:any) => {
    if (res.code === 200) {
      if(res.data){
          router.push({
          path: '/ai_percision/answer_questions',
          query: {
            reportId: res.data,
          }
        })
      }
      console.log(res.data,"创建成功1111！")

    } 
  })
}
const testList = ref([] as any[])
// 计算属性：格式化章节路径文本
const chapterPathText = computed(() => {
  // 依赖于强制更新计数器，确保响应式更新
  const _ = forceUpdatePath.value
  
  console.log("=== chapterPathText计算开始 (更新计数:", forceUpdatePath.value, ") ===")
  console.log("当前chapterPath:", chapterPath.value)
  console.log("当前chapterId:", chapterId.value)
  console.log("当前options长度:", options.value?.length)
  
  // 优先使用chapterPath
  if (chapterPath.value && chapterPath.value.length > 0) {
    console.log("使用现有的chapterPath，路径长度:", chapterPath.value.length)
    const pathText = buildPathText(chapterPath.value)
    console.log("从chapterPath生成的路径文本:", pathText)
    if (pathText && pathText !== "") {
      console.log("=== 返回chapterPath路径 ===")
      return pathText
    }
  }
  
  // 如果没有路径但有选中的章节ID，尝试从options中查找
  if (chapterId.value && options.value && options.value.length > 0) {
    console.log("尝试从options中重新构建路径")
    
    // 方法1：使用getFullChapterPath直接获取完整路径
    const fullPath = getFullChapterPath(options.value, chapterId.value)
    if (fullPath && fullPath.length > 0) {
      const pathText = buildPathText(fullPath)
      console.log("方法1 - 从getFullChapterPath得到的路径:", pathText)
      if (pathText && pathText !== "") {
        // 避免循环更新：只在路径确实不同时才更新
        if (JSON.stringify(chapterPath.value) !== JSON.stringify(fullPath)) {
          console.log("更新chapterPath以保持一致性")
          chapterPath.value = fullPath
        }
        console.log("=== 返回getFullChapterPath路径 ===")
        return pathText
      }
    }
    
    // 方法2：使用buildChapterPath作为备用
    console.log("方法1失败，尝试方法2")
    const targetChapter = findTargetChapter(options.value, chapterId.value)
    if (targetChapter) {
      console.log("找到目标章节:", targetChapter.name || targetChapter.chapterName)
      const foundPath = buildChapterPath(options.value, targetChapter)
      if (foundPath && foundPath.length > 0) {
        const pathText = buildPathText(foundPath)
        console.log("方法2 - 从buildChapterPath得到的路径:", pathText)
        if (pathText && pathText !== "") {
          // 避免循环更新：只在路径确实不同时才更新
          if (JSON.stringify(chapterPath.value) !== JSON.stringify(foundPath)) {
            console.log("更新chapterPath以保持一致性")
            chapterPath.value = foundPath
          }
          console.log("=== 返回buildChapterPath路径 ===")
          return pathText
        }
      }
    }
  }
  
  console.log("=== 返回默认文本 ===")
  return "请选择章节"
})

// 构建路径文本的辅助函数 - 优化版
const buildPathText = (pathArray) => {
  console.log("=== buildPathText 开始处理路径 ===")
  console.log("输入的路径数组:", pathArray)
  
  if (!pathArray || pathArray.length === 0) {
    console.log("路径数组为空，返回空字符串")
    return ""
  }
  
  // 处理路径数组，确保获取正确的章节名称
  const pathNames = pathArray
    .map((chapter, index) => {
      // 多重字段检查，确保获取到正确的名称
      let name = ''
      
      // 按优先级获取名称
      if (chapter.chapterName && chapter.chapterName.trim()) {
        name = chapter.chapterName.trim()
      } else if (chapter.name && chapter.name.trim()) {
        name = chapter.name.trim()
      } else if (chapter.title && chapter.title.trim()) {
        name = chapter.title.trim()
      } else {
        name = `未命名章节${index + 1}`
      }
      
      console.log(`🔍 第${index + 1}级章节处理:`, {
        id: chapter.id,
        原始chapterName: chapter.chapterName,
        原始name: chapter.name,
        原始title: chapter.title,
        最终使用名称: name
      })
      
      return name
    })
    .filter(name => {
      // 过滤掉无效名称
      const isValid = name && 
                      name !== '' && 
                      name !== '未命名章节' && 
                      !name.startsWith('未命名章节') && 
                      name.trim().length > 0
      
      console.log(`🔍 章节名称验证: "${name}" -> ${isValid ? '✅有效' : '❌无效'}`)
      return isValid
    })
  
  console.log("📋 过滤后的有效路径名称:", pathNames)
  
  // 构建最终路径文本
  const result = pathNames.length > 0 ? pathNames.join(' > ') : ""
  
  console.log(`🎯 最终生成的路径文本: "${result}"`)
  console.log("=== buildPathText 处理完成 ===")
  
  // 特殊情况处理：如果期望的是 "第一单元 100以内数加与减（二）>图书角" 格式
  if (result && pathNames.length >= 2) {
    console.log(`✨ 检测到多层级路径 (${pathNames.length}层)，应该显示: ${result}`)
  }
  
  return result
}

// 查找目标章节的辅助函数
const findTargetChapter = (chapters, targetId) => {
  console.log("findTargetChapter查找目标ID:", targetId)
  
  const search = (nodes, path = []) => {
    for (const chapter of nodes) {
      const currentPath:any = [...path, chapter]
      console.log("检查章节:", chapter.id, chapter.name || chapter.chapterName)
      
      if (chapter.id === targetId) {
        console.log("找到目标章节，返回完整路径:", currentPath.map(c => c.name || c.chapterName))
        return { chapter, fullPath: currentPath }
      }
      
      if (chapter.children && chapter.children.length > 0) {
        const result = search(chapter.children, currentPath)
        if (result) return result
      }
    }
    return null
  }
  
  const result = search(chapters)
  return result ? result.chapter : null
}

// 调试章节数据结构的函数
const debugChapterStructure = (chapters, level = 0) => {
  const indent = "  ".repeat(level)
  console.log(`${indent}=== 第${level}层章节结构 ===`)
  
  if (!chapters || chapters.length === 0) {
    console.log(`${indent}无章节数据`)
    return
  }
  
  chapters.forEach((chapter, index) => {
    console.log(`${indent}章节${index + 1}:`, {
      id: chapter.id,
      chapterName: chapter.chapterName,
      name: chapter.name,
      title: chapter.title,
      hasChildren: !!(chapter.children && chapter.children.length > 0),
      childrenCount: chapter.children?.length || 0
    })
    
    if (chapter.children && chapter.children.length > 0) {
      debugChapterStructure(chapter.children, level + 1)
    }
  })
}

// 新增：直接获取完整路径的函数
const getFullChapterPath = (chapters, targetId) => {
  console.log("getFullChapterPath查找目标ID:", targetId)
  
  // 首先调试章节结构
  console.log("调试章节结构:")
  debugChapterStructure(chapters)
  
  const search = (nodes, path = []) => {
    for (const chapter of nodes) {
      const currentPath:any = [...path, chapter]
      console.log("检查章节路径:", chapter.id, chapter.name || chapter.chapterName)
      
      if (chapter.id === targetId) {
        console.log("✅ 找到完整路径:")
        currentPath.forEach((node, idx) => {
          console.log(`  级别${idx + 1}: ${node.name || node.chapterName || node.title} (ID: ${node.id})`)
        })
        return currentPath
      }
      
      if (chapter.children && chapter.children.length > 0) {
        const result = search(chapter.children, currentPath)
        if (result) return result
      }
    }
    return null
  }
  
  const result = search(chapters)
  console.log("getFullChapterPath最终结果:", result?.length || 0, "个层级")
  return result
}

const chapterData = reactive<any>({
  percentage1: null,
  percentage1i: null,
  strong1: false,
  rate1: 1,
  percentage2: null,
  percentage2i: null,
  strong2: false,
  rate2: 2,
  percentage3: null,
  percentage3i: null,
  strong3: false,
  rate3: 3,
  percentage0: null,
  percentage0i: null,
  strong0: false,
})
const options = ref([])

// 课程数据结构
const lessonData = ref([
  {
    id: 1,
    name: '第1课',
    correctRate: 89.4,
    studyStatus: 0,
    status: 5,
    knowledgePoints: [
      {
        id: 'k1',
        name: '知识点名称',
        basicProgress: 89.4,
        advancedProgress: 33,
        isCompleted: false,
        completionLevel: null
      },
      {
        id: 'k2',
        name: '知识点名称知识点名称知识点名称知识点名称知识点名称知识点名称知识点名称知识点名称知识点名称知识点名称知识点名称知识点名称知识点名称知识点名称知识点名称知识点名称知识点名称知识点名称知识点名称知识点名称知识点名称知识点名称知识点名称知识点名称',
        basicProgress: 89.4,
        advancedProgress: 33,
        isCompleted: false,
        completionLevel: null
      }
    ]
  },
  {
    id: 2,
    name: '第2课',
    correctRate: 100,
    studyStatus: 1,
    status: 1,
    knowledgePoints: [
      {
        id: 'k3',
        name: '知识点名称',
        basicProgress: 100,
        advancedProgress: 100,
        isCompleted: true,
        completionLevel: 'perfect'
      },
      {
        id: 'k4',
        name: '知识点名称知识点名称知识点名称知识点名称知知识点名称知识点名称知识点名称知识点名称知识点名称识点名称',
        basicProgress: 100,
        advancedProgress: 100,
        isCompleted: true,
        completionLevel: 'perfect'
      }
    ]
  },
  {
    id: 3,
    name: '第3课',
    correctRate: 0,
    studyStatus: 0,
    status: 5,
    knowledgePoints: [
      {
        id: 'k5',
        name: '知识点名称',
        basicProgress: 0,
        advancedProgress: 0,
        isCompleted: false,
        completionLevel: null
      },
      {
        id: 'k6',
        name: '知识点名称知识点名称知识点名称知识点名称知识点名称',
        basicProgress: 0,
        advancedProgress: 0,
        isCompleted: false,
        completionLevel: null
      }
    ]
  }
])
// 处理挑战按钮点击
const handleChallengeClick = (lesson: any) => {
  console.log(lesson.knowledgePoints,"lessonlessonlessonlessonlessonlessonlesson")
  
  // 获取当前课程的知识点ID列表
  const pointIds = lesson.knowledgePoints.map((point: any) => point.id);
  
  // 收集选中的子级ID到数组中
  const currentChildIds = lesson.knowledgePoints
    .filter((point: any) => point.id) // 确保ID存在
    .map((point: any) => point.id);
  
  // 将新的子级ID添加到全局数组中（去重）
  currentChildIds.forEach((id: string) => {
    if (!selectedChildIds.value.includes(id)) {
      selectedChildIds.value.push(id);
    }
  });
  
  
  addTrainingApi({
    bookId: query.bookId,
    pointIds: selectedChildIds.value,
    subject:query.subject,
    chapterId:chapterId.value,
    level:'1'

  }).then((res:any) => {
    if (res.code === 200) {
      if(res.data){
        trainingId.value = res.data
          trainingInfoApi({
           trainingId: res.data
         }).then((res: any) => {
           if (res.code == 200) {
             console.log(res.data,"获取训练信息成功！")
             
             // 处理时间转换：将秒转换为分钟
             const timeInSeconds = res.data.times || 0;
             const timeInMinutes = convertSecondsToMinutes(timeInSeconds);
             
             // 显示挑战弹窗
             knowledgeList.value = res.data.pointItems
             challengePop.value = true;
             knowledgeOll.times = timeInMinutes; // 存储转换后的分钟数
             knowledgeOll.quesCount = res?.data?.quesCount;
           }

       })
    } 
  }
  })

  
  // 设置弹窗数据
  selectedLessonForChallenge.value = {
    id: lesson.id,
    knowledgePoints: lesson.knowledgePoints,
    pointIds: pointIds,
    currentChildIds: currentChildIds // 添加当前选中的子级ID
  };
}

// 存储当前选中的课程数据，用于挑战
const selectedLessonForChallenge = ref<any>(null);

// 清空选中的子级ID数组
const clearSelectedChildIds = () => {
  console.log("🧹 清空选中的子级ID数组，之前有:", selectedChildIds.value.length, "个ID");
  selectedChildIds.value = [];
  console.log("✅ 已清空选中ID数组");
};

// 查看当前选中的子级ID状态
const logSelectedChildIds = () => {
  console.log("=== 当前选中子级ID状态 ===");
  console.log("📋 选中的ID数组:", selectedChildIds.value);
  console.log("📊 选中数量:", selectedChildIds.value.length);
  console.log("🔗 ID列表:", selectedChildIds.value.join(', '));
  console.log("=== 状态查看完成 ===");
};

// 将秒转换为分钟的辅助函数
const convertSecondsToMinutes = (seconds: number): number => {
  if (!seconds || seconds <= 0) {
    console.log("⚠️ 无效的秒数，使用默认值2分钟");
    return 2; // 默认2分钟
  }
  
  const minutes = Math.ceil(seconds / 60); // 向上取整
  console.log(`⏱️ 时间转换详情: ${seconds}秒 = ${(seconds / 60).toFixed(1)}分钟 → ${minutes}分钟(向上取整)`);
  
  // 确保最少1分钟
  return Math.max(minutes, 1);
};



const onChallenge = () => {

  // if (!selectedLessonForChallenge.value) return;
  
  // 输出挑战时的选中ID信息
  console.log("🚀 开始挑战！");
  console.log("📋 使用的子级ID:", selectedLessonForChallenge.value.currentChildIds);
  console.log("📊 累计选中的所有ID:", selectedChildIds.value);
  
  // router.push({
  //   path: '/ai_percision/foundation_report',
  //   query: {
  //     data: dataEncrypt({
  //       sourceId: 24796,
  //       chapterId: chapterId.value,
  //       pointId: selectedLessonForChallenge.value.pointIds || [],
  //       subject: subjectObj.value.id,
  //       type: 1,
  //       step: 1,
  //       contentType: query.contentType,
  //       isPromote: 0
  //     }),
  //     type: 1,
  //     contentType: query.contentType
  //   }
  // });
    router.push({
      path: '/ai_percision/answer_training',
      // path: '/ai_percision/entrance_assessment/doing_exercises',
      query: {
        data: dataEncrypt({
          reportId: trainingId.value ,
          pageSource: '1',
          bookId: subjectObj.value.bookId,
          chapterId:chapterId.value
        }),
      }
    })
}

const getUrl = (item: any) => {
    let url = 'pen'
    if (item > 90) {
        url = 'strong'
    }
    return new URL(`../../../assets/img/percision/${url}.png`, import.meta.url).href //静态资源引入为url，相当于require()
}
// 递归查找章节，并返回包含该章节的完整路径
const findChapterById = (chapters:any, targetId:any, path:any = []) => {
  let deepestPath: any[] | null = null;

  for (const chapter of chapters) {
    // 确保章节有chapterName属性
    if (!chapter.chapterName && chapter.name) {
      chapter.chapterName = chapter.name
    }
    
    // 创建当前路径的副本
    const currentPath:any = [...path, chapter]

    // 如果找到目标章节，记录当前路径
    if (chapter.id === targetId) {
      // 记录找到的路径，但不立即返回
      deepestPath = currentPath;
      // 更新全局的章节路径
      chapterPath.value = deepestPath;
    }

    // 无论是否已找到匹配节点，都继续递归查找子节点
    if (chapter.children && chapter.children.length > 0) {
      const childResult: any[] | null = findChapterById(chapter.children, targetId, currentPath)
      // 如果子节点中找到了结果，优先使用子节点的结果（更深层次）
      if (childResult) {
        // 如果已经有记录但找到了更深的路径，或者还没有记录
        if (!deepestPath || childResult.length > deepestPath.length) {
          deepestPath = childResult;
          // 更新全局的章节路径
          chapterPath.value = deepestPath;
        }
      }
    }
  }

  return deepestPath;
}

onMounted(async () => {
  console.log("🚀 basic_training onMounted 开始")
  
  // 从路由参数中获取章节ID
  let targetChapterId = null

  if (query.chapterId) {
    targetChapterId = query.chapterId
  } else if (query.data) {
    try {
      const decryptedData = dataDecrypt(query.data)
      if (decryptedData && decryptedData.chapterId) {
        targetChapterId = decryptedData.chapterId
      }
    } catch (error) {
      console.error('Failed to decrypt query data:', error)
    }
  }

  console.log("📍 路由中的章节ID:", targetChapterId)

  // 获取章节列表
  await getBookChapterList()
  
  // 如果找到目标章节ID，设置选中状态
  if (targetChapterId) {
    console.log("🎯 使用路由指定的章节ID")
    chapterId.value = targetChapterId
    routeHasChapterId.value = true

    const foundPath = findChapterById(options.value, targetChapterId)
    if (foundPath && foundPath.length > 0) {
      const targetChapter = foundPath[foundPath.length - 1]
      pageStatus.value = targetChapter.name !== "单元测试"
      chapterName.value = targetChapter.name || targetChapter.chapterName
    }
    
    if(!pageStatus.value) {
      getChapterReportList()
    } else {
      getMastery()
    }
  } else {
    console.log("🎯 没有路由章节ID，使用默认选中逻辑")
    // handleDefaultSelection 已经在 getBookChapterList 中被调用了
    // 延迟加载数据，确保选中逻辑完成后再加载
    setTimeout(() => {
      if(!pageStatus.value) {
        console.log("📊 延迟加载试卷数据")
        getChapterReportList()
      } else {
        console.log("📊 延迟加载知识点数据")
        getMastery()
      }
    }, 100)
  }
  
  console.log("✅ basic_training onMounted 完成")
})

// 查找第一个叶子节点（参考knowledge_graph_detail页面实现）
const findFirstLeafChapter = (chapters) => {
  if (!chapters || chapters.length === 0) return null
  
  for (const chapter of chapters) {
    if (chapter.children && chapter.children.length > 0) {
      const leafChapter = findFirstLeafChapter(chapter.children)
      if (leafChapter) return leafChapter
    } else {
      return chapter
    }
  }
  return null
}

// 手动构建章节路径
const buildChapterPath = (chapters, targetChapter) => {
  console.log("buildChapterPath开始")
  console.log("目标章节:", targetChapter)
  console.log("章节树结构:", chapters)
  
  const findPath = (nodes, target, currentPath = []) => {
    console.log("查找路径中，当前层级:", nodes?.length, "当前路径长度:", currentPath.length)
    
    for (const node of nodes) {
      const newPath:any = [...currentPath, node]
      console.log("检查节点:", node.id, node.name || node.chapterName, "目标ID:", target.id)
      
      // 如果找到目标节点
      if (node.id === target.id) {
        console.log("✅ 找到目标节点！完整路径:")
        newPath.forEach((pathNode, index) => {
          console.log(`  ${index + 1}. ${pathNode.name || pathNode.chapterName} (ID: ${pathNode.id})`)
        })
        return newPath
      }
      
      // 如果有子节点，递归查找
      if (node.children && node.children.length > 0) {
        console.log(`递归查找子节点，子节点数量: ${node.children.length}`)
        const result = findPath(node.children, target, newPath)
        if (result) {
          console.log("从子节点中找到结果，返回路径长度:", result.length)
          return result
        }
      }
    }
    console.log("当前层级没有找到目标节点")
    return null
  }
  
  const result = findPath(chapters, targetChapter)
  console.log("buildChapterPath最终结果:", result?.length || 0, "个节点")
  return result
}

// 处理默认选中逻辑（参考knowledge_graph_detail页面实现）
const handleDefaultSelection = () => {
  console.log("🎯 handleDefaultSelection 开始执行")
  console.log("  - options长度:", options.value?.length)
  console.log("  - 当前chapterId:", chapterId.value)
  
  if (options.value.length > 0) {
    console.log("✅ 有章节数据，开始选择第一个选项")
    // 没有存储信息，使用options第一条数据作为默认值
    selectFirstOption()
  } else {
    console.log("❌ 没有章节数据，无法选择")
  }
}

// 选择第一个可用选项（参考knowledge_graph_detail页面实现）
const selectFirstOption = () => {
  const firstOption = findFirstLeafChapter(options.value)
  if (firstOption) {
    const defaultId = firstOption.id
    const defaultName = firstOption.name || firstOption.chapterName
    
    console.log("🎯 selectFirstOption 找到第一个叶子节点:")
    console.log("  - ID:", defaultId)
    console.log("  - 名称:", defaultName)
    console.log("  - 节点数据:", firstOption)
    
    // 先设置值
    chapterId.value = defaultId
    chapterName.value = defaultName
    
    // 设置页面状态
    pageStatus.value = defaultName !== "单元测试"
    
    // 设置章节路径
    const foundPath = getFullChapterPath(options.value, defaultId) || 
                     buildChapterPath(options.value, firstOption) || 
                     findChapterById(options.value, defaultId) || 
                     [firstOption]
    
    chapterPath.value = foundPath
    
    console.log("🚀 准备调用setChapterId")
    
    // 使用nextTick确保DOM更新后再调用
    nextTick(() => {
      setChapterId(firstOption, defaultName)
      // 强制更新路径计算
      forceUpdatePath.value++
    })
  } else {
    console.log("❌ selectFirstOption 未找到可用的叶子节点")
  }
}
const pageData = reactive({
  total: 0,
  current: 1,
  size: 10
})

const pageClick = (val: number) => {
  pageData.current = val
  getChapterReportList()
}
// 单元测试
const dialogVisible = ref(false)
const currentSizeChange = (currentPage: number, pageSize: number) => {
  pageData.current = currentPage
  pageData.size = pageSize
  // getChapterReportList()
}
//下载试卷
const downloadTrestDialogRef = ref()
const dowmloadData = reactive({
    id: '',
    title: ''
})
const handleDownload = ({ id, title }: any) => {
  Object.assign(dowmloadData, {
    id: id,
    title: title
  })
  dialogVisible.value = true
  nextTick(() => {
    downloadTrestDialogRef.value.dialogShow()
  })
}
const testDetail = (data: any) => {
  router.push({
    path: '/ai_percision/knowledge_graph_detail_unit/paper_detailU',
    query: {
      data: dataEncrypt({
        reportId: data.id,
        pageSource: '3'
      }),
    }
  })
}

//获取试卷列表
const getChapterReportList = async() => {
  loading2.value = true
  try {
    const res: any = await getChapterReportListApi({
      chapterId: chapterId.value,
      bookId: subjectObj.value.bookId,
      isSmall: 1,
      type: 1,
      size: pageData.size,
      current: pageData.current
    })
    if(res.code == 200) {
      testList.value = res.data.records || []
      pageData.total = Number(res.data.total)
    }
    loading2.value = false
  } catch (error) {
    testList.value = []
    console.log(error)
    loading2.value = false
  }
}

//获取章节列表
const getBookChapterList = async() => {
  if(query.contentType == 'historyTask'){
    subjectObj.value.bookId = query.bookId
  }
  loading.value = true
  loading2.value = true
  try {
    const res: any = await getChapterListApi({
      bookId:subjectObj.value.bookId,
      hierarchy: 3,
      type: query.type,
      chapterIds: query?.resourceIds?query?.chapterId:''
    })
    if(res.code == 200) {
      
      // 确保所有节点都有chapterName属性
      const processChapterData = (chapters) => {
        return chapters.map(chapter => {
          // 确保每个节点都有chapterName属性
          const processedChapter = {
            ...chapter,
            chapterName: chapter.chapterName || chapter.name || chapter.title || '未命名章节'
          }
          
          // 递归处理子节点
          if (processedChapter.children && processedChapter.children.length > 0) {
            processedChapter.children = processChapterData(processedChapter.children)
          }
          
          return processedChapter
        })
      }
      
      // 处理API返回的数据，确保所有节点都有chapterName
      const processedData = processChapterData(res.data || [])
      options.value = processedData
      
      console.log("章节数据已加载:", processedData.length, "项")
      console.log("第一个章节:", processedData[0])
      
      // 处理默认选中逻辑
      handleDefaultSelection()
    }
    loading.value = false
    return res.data || []
  } catch (error) {
    options.value = []
    console.log(error)
    loading.value = false
    return []
  }
}
const getLast = (data: any) => {
  let id = ""
  if(data.children && data.children.length > 0) {
    // 构建路径
    const path = [data]
    const lastId = getLast(data.children[0])
    
    // 如果是第一次调用（没有路径设置），则设置路径
    if (chapterPath.value.length === 0) {
      // 查找完整路径并设置
      findChapterById(options.value, lastId)
    }
    
    return lastId
  } else {
    id = data.id
    
    // 如果是第一次调用（没有路径设置），则设置路径
    if (chapterPath.value.length === 0) {
      chapterPath.value = [data]
    }
    
    return id
  }
}
// 左侧选中Id
const setChapterId = (data: any, name: string) => {
  console.log("setChapterId被调用，data:", data, "name:", name)
  
  // 使用chapterName作为节点名称，如果不存在则回退到name
  const nodeName = data.chapterName || data.name
  pageStatus.value = nodeName != "单元测试"
  
  // 根据iswen属性决定使用哪个ID字段
  const nodeId = data.id || data.chapterId
  chapterId.value = nodeId
  
  // 设置章节名称
  chapterName.value = name || nodeName
  
  console.log("设置chapterId为:", nodeId)
  console.log("设置chapterName为:", chapterName.value)

  // 当用户手动选择章节时，重置标记
  routeHasChapterId.value = false

  // 更新章节路径 - 确保获取完整的层级路径
  console.log("=== setChapterId 开始更新章节路径 ===")
  console.log("节点ID:", nodeId)
  console.log("节点数据:", data)
  
  // 方法1：使用getFullChapterPath直接获取完整路径
  let foundPath = getFullChapterPath(options.value, nodeId)
  console.log("方法1 - getFullChapterPath返回的路径:", foundPath?.length || 0, "个节点")
  
  // 方法2：如果方法1失败，使用buildChapterPath
  if (!foundPath || foundPath.length === 0) {
    console.log("方法1失败，尝试方法2 - buildChapterPath")
    foundPath = buildChapterPath(options.value, data)
    console.log("方法2 - buildChapterPath返回的路径:", foundPath?.length || 0, "个节点")
  }
  
  // 方法3：如果方法2也失败，使用findChapterById
  if (!foundPath || foundPath.length === 0) {
    console.log("方法2失败，尝试方法3 - findChapterById")
    foundPath = findChapterById(options.value, nodeId)
    console.log("方法3 - findChapterById返回的路径:", foundPath?.length || 0, "个节点")
  }
  
  // 设置章节路径
  if (foundPath && foundPath.length > 0) {
    chapterPath.value = foundPath
    console.log("✅ 设置完整章节路径:")
    foundPath.forEach((node, index) => {
      console.log(`  ${index + 1}. ${node.name || node.chapterName || node.title} (ID: ${node.id})`)
    })
  } else {
    // 最后的备用方案，至少设置当前节点
    chapterPath.value = [data]
    console.log("⚠️ 使用单个节点作为路径:", nodeName)
  }
  
  console.log("=== setChapterId 路径更新完成 ===", "\n")
    // 根据页面状态决定加载什么数据
    if(!pageStatus.value) {
      console.log("📊 加载试卷数据")
      getChapterReportList()
    } else {
      console.log("📊 加载知识点数据")
      getMastery()
  }
  // 可选：更新URL，保持状态同步（不刷新页面）
  router.replace({
    query: {
      ...route.query,
      chapterId: data.id
    }
  })
}

// 知识点列表
const getMastery = async() => {
  console.log("📊 getMastery 开始获取知识点数据")
  console.log("  - 当前chapterId:", chapterId.value)
  
  loading2.value = true
  try {
    const res: any = await getpointListApi({
      chapterId: chapterId.value
    })
    
    console.log("📊 知识点API响应:", res)

    if (res.code === 200 && res.data && Array.isArray(res.data)) {
      // 将API返回的知识点数据直接使用，保留原始属性
      lessonData.value = res.data.map((point: any) => ({
        ...point,
        // 确保关键属性存在
        correctRate: point.correctRate || 0,
        studyStatus: point.studyStatus || 0,
        status: point.status || 5, // 默认为未测试
      }));
      

      // 如果API返回了掌握度数据，则更新chapterData
      if (res.data[0] && res.data[0].trainCollect) {
        res.data[0].trainCollect.map((item:any) => {
          let num = Number(item.mastery)
          if (item.type == 1) {
            chapterData.percentage1 = num
            chapterData.percentage1i = parseFloat((num).toFixed(2))
          } else if (item.type == 2) {
            chapterData.percentage2 = num
            chapterData.percentage2i = parseFloat((num).toFixed(2))
          } else if (item.type == 3) {
            chapterData.percentage3 = num
            chapterData.percentage3i = parseFloat((num).toFixed(2))
          } else if (item.type == 0) {
            chapterData.percentage0 = num
            chapterData.percentage0i = parseFloat((num).toFixed(2))
          }
        })
      } else {
        // 如果没有掌握度数据，则根据知识点状态计算掌握度
        const totalPoints = lessonData.value.length;
        if (totalPoints > 0) {
          const masteredPoints = lessonData.value.filter(point => point.status === 1).length;
          const masteryPercentage = (masteredPoints / totalPoints) * 100;
          
          chapterData.percentage1 = masteryPercentage;
          chapterData.percentage1i = parseFloat(masteryPercentage.toFixed(2));
        } else {
          resetChapterData();
        }
      }
    } else {
      // 如果API没有返回有效数据，则重置
      lessonData.value = [];
      resetChapterData();
    }
    loading2.value = false;
  } catch (error) {
    console.error('获取知识点数据失败:', error);
    lessonData.value = [];
    resetChapterData();
    loading2.value = false;
  }
}

// 重置章节数据
const resetChapterData = () => {
  chapterData.percentage1 = 0;
  chapterData.percentage1i = 0;
  chapterData.percentage2 = 0;
  chapterData.percentage2i = 0;
  chapterData.percentage3 = 0;
  chapterData.percentage3i = 0;
  chapterData.percentage0 = 0;
  chapterData.percentage0i = 0;
}
const toPractice = (val:any) => {
     router.push({
        name: 'TeachRoomTeachVideo',
        query: { 
          id: val.id,
          source: 'analysis',
          subject: query.subject,
        } 
    })
}
const bookVersionName = computed(() => {
  return subjectObj.value.editionName + learnNow.value.gradeName + (subjectObj.value.termName?subjectObj.value.termName:"")
})
// 获取通关状态
const getCompletionStatus = (knowledge: any) => {
  return knowledge.isCompleted && knowledge.completionLevel
}

// 获取通关徽章图标
const getCompletionBadge = (knowledge: any) => {
  if (knowledge.completionLevel === 'perfect' && knowledge.basicProgress >= 100 && knowledge.advancedProgress >= 100) {
    return new URL(`../../../assets/img/percision/training/组合 <EMAIL>`, import.meta.url).href
  }
  return ''
}

// 判断是否可以显示挑战按钮
const canShowChallengeButton = (lesson: any) => {
  // 检查课程中是否有知识点达到挑战条件（基础训练完成但进阶训练未完成）
  return lesson.knowledgePoints.some((knowledge: any) => 
    knowledge.correctRate >= 70 && knowledge.studyStatus !== 1
  )
}

// 获取挑战按钮文本
const getChallengeButtonText = (lesson: any) => {
  if (!lesson.knowledgePoints || lesson.knowledgePoints.length === 0) {
    return '挑战青铜'
  }
  
  const allCompleted = lesson.knowledgePoints.every((knowledge: any) => 
    knowledge.correctRate >= 100 && knowledge.studyStatus === 1
  )
  
  if (allCompleted) {
    return '完美掌握'
  } else if (canShowChallengeButton(lesson)) {
    return '再次挑战'
  } else {
    return '挑战青铜'
  }
}

// 获取星级图标
const getStarIcon = (filled: boolean) => {
  return filled
    ? new URL(`../../../assets/img/percision/star1.png`, import.meta.url).href
    : new URL(`../../../assets/img/percision/star0.png`, import.meta.url).href
}

// 获取操作按钮图标
const getActionIcon = (percentage: number) => {
  if (percentage > 90) {
    return new URL(`../../../assets/img/percision/strong.png`, import.meta.url).href
  }
  return new URL(`../../../assets/img/percision/pen.png`, import.meta.url).href
}

// 获取成就徽章图标
const getMedalIcon = (type: string) => {
  const medalMap = {
    'basic': 'medal_2.png',
    'advanced': 'medal_3.png',
    'comprehensive': 'medal_1.png'
  }
  return new URL(`../../../assets/img/percision/training/${medalMap[type] || 'medal_1.png'}`, import.meta.url).href
}
// 新增辅助函数
const getHexagonClass = (percentage: number) => {
  if (percentage >= 90) return 'excellent'
  if (percentage >= 70) return 'good'
  if (percentage >= 50) return 'average'
  return 'poor'
}

// 获取掌握度状态类名
const getMasteryClass = (percentage: number) => {
  if (percentage >= 90) return 'excellent'
  if (percentage >= 70) return 'good'
  if (percentage >= 50) return 'average'
  return 'poor'
}

// 获取掌握度文本
const getMasteryText = (percentage: number) => {
  if (percentage >= 90) return '优秀掌握'
  if (percentage >= 70) return '良好掌握'
  if (percentage >= 50) return '一般掌握'
  return '需要加强'
}

// 获取小奖牌图标
const getSmallMedalIcon = (type: number) => {
  const medalMap = {
    1: 'medal_small_1.png',
    2: 'medal_small_2.png',
    3: 'medal_small_3.png',
    4: 'medal_small_4.png',
    5: 'medal_small_5.png',
    6: 'medal_small_6.png',
    7: 'medal_small_7.png',
    8: 'medal_small_8.png'
  }
  return new URL(`../../../assets/img/percision/training/${medalMap[type] || 'medal_small_1.png'}`, import.meta.url).href
}

// 获取完美掌握奖牌
const getPerfectMedalIcon = () => {
  return new URL(`../../../assets/img/percision/training/medal_1.png`, import.meta.url).href
}

// 判断是否显示挑战徽章
const shouldShowChallengeBadge = (percentage1: number, percentage2: number) => {
  return percentage1 >= 70 && percentage2 < 70
}

// 获取挑战徽章图标
const getChallengeBadgeIcon = () => {
  return new URL(`../../../assets/img/percision/training/组合 <EMAIL>`, import.meta.url).href
}

// 获取再次挑战徽章图标
const getRetryBadgeIcon = () => {
  return new URL(`../../../assets/img/percision/training/组合 <EMAIL>`, import.meta.url).href
}

// 获取完美掌握徽章图标
const getPerfectBadgeIcon = () => {
  return new URL(`../../../assets/img/percision/training/组合 <EMAIL>`, import.meta.url).href
}

const onMark = () => {
  router.push({
    path: '/ai_percision/knowledge_hotspot',
    query:{
      bookId:subjectObj.value.bookId,
      subject:query.subject
    }
  })
}
// 获取六边形背景类名
const getHexagonBgClass = (percentage: number, type: string) => {
  let baseClass = type
  if (percentage >= 90) {
    baseClass += ' excellent'
  } else if (percentage >= 70) {
    baseClass += ' good'
  } else if (percentage >= 50) {
    baseClass += ' average'
  } else {
    baseClass += ' poor'
  }
  return baseClass
}

// 获取知识点状态类名
const getKnowledgeStatusClass = (knowledge: any) => {
  // 根据status值确定样式类名
  switch (knowledge.status) {
    case 1: return 'status-mastered'; // 已掌握
    case 2: return 'status-learning'; // 学习中
    case 3: return 'status-failed';   // 未掌握
    case 4: return 'status-testing';  // 测试中
    case 5: return 'status-untested'; // 未测试
    default: return 'status-unknown'; // 未知状态
  }
}

// 获取知识点状态文本
const getKnowledgeStatusText = (knowledge: any) => {
  // 根据status值确定显示文本
  switch (knowledge.status) {
    case 1: return '已掌握';
    case 2: return '学习中';
    case 3: return '未掌握';
    case 4: return '测试中';
    case 5: return '未测试';
    default: return '未知';
  }
}

const onModify = () =>{
  console.log(learnUsers[0].learnId,"learnUserslearnUserslearnUsers")
    router.push({
    path: '/user/user_add',
    query: {
      learnId: learnUsers[0].learnId,
      pageType:'edit'
    }
  })
}

// 知识点详情弹窗相关
const knowledgeDetailVisible = ref(false)
const selectedKnowledge = ref<any>(null)

const showKnowledgeDetail = (knowledge: any) => {
  selectedKnowledge.value = knowledge
  knowledgeDetailVisible.value = true
}

const handleDetailClose = () => {
  knowledgeDetailVisible.value = false
  selectedKnowledge.value = null
}

// 获取知识点难度文本
const getDifficultyText = (point: any) => {
  // 根据知识点的难度值返回对应的文本
  // 这里假设难度值在1-3之间，1为容易，2为中等，3为困难
  const difficultyLevel = point.difficulty || 1;
  
  switch (difficultyLevel) {
    case 1:
      return '容易';
    case 2:
      return '中等';
    case 3:
      return '困难';
    default:
      return '容易';
  }
}

// 计算总题量
const getTotalQuestions = () => {
  if (!selectedLessonForChallenge.value || !selectedLessonForChallenge.value.knowledgePoints) {
    return 6; // 默认值
  }
  
  // 计算所有知识点的题量总和
  return selectedLessonForChallenge.value.knowledgePoints.reduce((total: number, point: any) => {
    return total + (point.total || 3); // 如果没有题量数据，默认为3题
  }, 0);
}

// 计算预估完成时间（分钟）
const getEstimatedTime = () => {
  const totalQuestions = getTotalQuestions();
  // 假设每题平均需要1分钟
  return Math.max(Math.ceil(totalQuestions), 6); // 至少6分钟
}

// Watch for changes in options data - 备用的选中逻辑
watch(() => options.value, (newOptions) => {
  console.log("📊 options数据变化:", newOptions?.length, "当前chapterId:", chapterId.value)
  
  // 如果options数据变化但仍然没有chapterId，尝试再次自动选择
  if (newOptions && newOptions.length > 0 && !chapterId.value && !routeHasChapterId.value) {
    console.log("🎯 watch检测到需要自动选择子级目录")
    
    // 延迟执行，确保DOM完全渲染和树组件初始化完成
    setTimeout(() => {
      if (!chapterId.value) { // 再次检查，避免重复设置
        console.log("🚀 执行延迟自动选择")
        handleDefaultSelection()
      } else {
        console.log("✅ 已有chapterId，跳过自动选择")
      }
    }, 500) // 增加延迟时间，确保树组件完全初始化
  }
}, { immediate: true })

</script>

<style scoped lang="scss">
.training-page {
  display: flex;
  min-height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height));
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.left-sidebar {
  position: relative;
  background: white;
  border-radius: 0 20px 20px 0;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);

  .sidebar-title {
    position: absolute;
    left: -14px;
    top: 10px;
    width: 179px;
    height: 46px;
    line-height: 46px;
    text-align: center;
    background: linear-gradient(135deg, #00c9a3 0%, #00a085 100%);
    color: white;
    font-size: 20px;
    font-weight: 700;
    border-radius: 0 10px 10px 0;
    z-index: 10;

    .title-decoration {
      position: absolute;
      bottom: -14px;
      left: 0;
      width: 0;
      height: 0;
      border-left: 7px solid transparent;
      border-right: 7px solid #00886e;
      border-top: 7px solid #00886e;
      border-bottom: 7px solid transparent;
    }
  }
}
.test-box {
  height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height) - 11.875rem);
  width: 54.5rem;
  margin-left: 1.5625rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: .625rem;
  position: relative;
  .test-wrap{
      width: calc(100% - .5rem);
      .hui-line{
        width: calc(100% - 1.75rem);
        border-bottom: .0625rem dashed #EAEAEA;
        margin: 0 0 0 .875rem;
        float: left;
      }
    }
    &-item {
      width: 100%;
      height: 6.875rem;
      display: flex;
      padding: 1.25rem 1.25rem 1.25rem 1.25rem;
      box-sizing: border-box;
      &:hover {
        background: #effdfb;
      }
      &-img {
        width: 3.1875rem;
        height: 100%;
        font-size: .75rem;
        background-image: url(@/assets/img/percision/test-img.png);
        background-size: 100%;
        background-repeat: no-repeat;
        text-align: center;
        span {
          display: inline-block;
          margin-top: 1.85rem;
          position: relative;
          left: .125rem;
        }
      }
    &-info {
      margin-left: 1rem;
      width: 40rem;
      margin-right: 1rem;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      &-title {
        color: #2a2b2a;
        font-size: 1rem;
        font-weight: 400;
      }
      &-data {
        div {
          height: 1.75rem;
          border-radius: .875rem;
          background: #fef8e9;
          color: #ef9d19;
          display: inline-block;
          box-sizing: border-box;
          padding: .375rem .75rem;
          font-size: .75rem;
          margin-right: .625rem;
        }
      }
    }
    &-btn {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      &-it {
        width: 5.25rem;
        height: 1.875rem;
        line-height: 1.875rem;
        border-radius: .25rem;
        font-size: .875rem;
        text-align: center;
        cursor: pointer;
        img {
          width: .875rem;
          height: .875rem;
        }
      }
    }
  }
  .learn-img {
    position: fixed;
    bottom: 1.875rem;
    left: 55%;
    width: 14.0625rem;
    height: 3.125rem;
  }
}
.btn {
  color: #ffffff;
  background: #00c9a3;
}
.grey-btn {
  background: #f5f5f5;
  color: #999999;
}
.red-text {
  color: #dd2a2a;
}
.blue-text {
  color: #009c7f;
}
.icon-sty {
  width: 1rem;
}
.main-content {
  flex: 1;
  margin-left: 10px;
  width: calc(100% - 378px);
  height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height));
  border-radius: 20px;
  overflow-x: hidden;
  overflow-y: auto;
  box-sizing: border-box;
  background-color: white;
  .content-head{
    position: relative;
    background: url('@/assets/img/percision/training/head-bg.png')center center no-repeat;
    background-size: cover;
    height: 121px;
    padding: 0 20px;
    .head-body{
      position: relative;
      padding: 20px 0 0 0px;
      font-size: 16px;
      font-weight: 600;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      .textbook{
        width: 24px;
        height: 30px;
        margin-right: 6px;
      }
      .head-title{
        margin-right: 10px;
      }
      .head-switch{
        display: flex;
        justify-content: center;
        align-items: center;
        width: 92px;
        height: 27px;
        font-size: 14px;
        font-weight: 600;
        color: #fff;
        background: #00957f;
        border-radius: 14px;
        cursor: pointer;
      }
    }
    .catalogue{
      background: #fff;
      border-radius: 22px;
      margin-top: 20px;
      line-height: 33px;
      padding-left: 16px;
      display: flex;
      span{
        color: rgba(102, 102, 102, 1);
        font-size: 16px;
      }
      img{
        width: 14px;
        height: 9px;
        margin: 12px 12px 12px auto;
      }
    }
    .superficiality{
      position: absolute;
      right: 0;
      top:0;
      width: 120px;
      height: 39px;
      cursor: pointer;
    }
    .head-select{
      position: relative;
      margin: 22px 0 0 20px;
      width: 882px;
      height: 33px;
    }
    .head-select :deep(.el-select__wrapper) {
        border-radius: 15px;
    }
  }
  .tip-content{
    padding: 0 16px;
    display: flex;
    justify-content: flex-start;
    height: 44px;
    align-items: center;
    .tip-avatar{
      width: 28px;
      height: 28px;
      margin-right: 10px;
    }
    .tip-text{
      color: #3294DB;
      font-size: 12px;
    }
  }
  .content-tip{
    position: relative;
    margin: 10px auto;
    width: 882px;
    height: 44px;
    border-radius: 4px;
    background: rgba(236, 247, 255, 1);
    .tip-bg{
      position: absolute;
      top: 0;
      left: 0;
      width: 882px;
      height: 44px;
    }
  }
}

.knowledge-training-list {
  padding:0 20px;
  .lesson-section {
    margin-bottom: 30px;
    
    background: #FAFBFD;
    // border-radius: 8px;
    // overflow: hidden;

    .lesson-tag {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 60px;
      height: 24px;
      text-align: center;
      border: 1px solid #5A85EC;
      background: rgba(238, 243, 253, 1);
      color: rgba(90, 133, 236, 1);
      font-size: 12px;
      font-weight: 400;
      // border-radius: 0 0 8px 0;
    }
  }
}

.knowledge-info {
 
  .knowledge-box {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
}

.knowledge-item {
  display: flex;
  align-items: center;
  margin: 0 20px;
  padding: 10px 0 30px 0;
  position: relative;
  transition: all 0.2s ease;
  border-bottom: 1px dashed rgba(234, 234, 234, 1);
  
  &:last-child {
    border-bottom: none;
  }

  .knowledge-name {
    width: 280px;
    padding-right: 20px;
    border-right: 1px solid #EAEAEA;
    margin-right: 16px;

    .knowledge-title {
      font-size: 14px;
      font-weight: 400;
      color: #333;
      line-height: 1.4;
      display: -webkit-box;
      -webkit-line-clamp: 2; /* 限制为两行 */
      line-clamp: 2; /* 标准属性，用于兼容性 */
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      padding-right: 30px; /* 为状态标签留出空间 */
    }
  }

  .progress-area {
    display: flex;
    align-items: center;
    margin-right: 20px;

    .hexagon-group {
      display: flex;
      gap: 20px;
      align-items: center;
    }
  }

  .action-area {
    display: flex;
    gap: 8px;
  }
}

.hexagon-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;

  .hexagon-bg {
    width: 50px;
    height: 50px;
    position: relative;
    background: url('@/assets/img/percision/training/medal_small_1.png')center no-repeat;
    background-size: cover;
    &.basic {
      // 基础训练 - 绿色系
      &.excellent {
        background: url('@/assets/img/percision/training/medal_small_2.png')center no-repeat;
      }
      &.good {
        background: url('@/assets/img/percision/training/medal_small_3.png')center no-repeat;

      }
      &.average {
        background: url('@/assets/img/percision/training/medal_small_4.png')center no-repeat;

      }
      &.poor {
        background: url('@/assets/img/percision/training/medal_small_5.png')center no-repeat;

      }
    }

    &.advanced {
      // 进阶训练 - 蓝色系
      &.excellent {
        background: url('@/assets/img/percision/training/medal_small_6.png')center no-repeat;

      }
      &.good {
        background: url('@/assets/img/percision/training/medal_small_7.png')center no-repeat;

      }
      &.average {
        background: url('@/assets/img/percision/training/medal_small_8.png')center no-repeat;
      }
      &.poor {
        background: url('@/assets/img/percision/training/medal_small_6.png')center no-repeat;

      }
    }

    .hexagon-content {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
      color: white;

      .percentage-text {
        font-size: 10px;
        font-weight: 600;
        margin-bottom: 2px;
        font-size: 12px;
        padding: 3px;
        position: absolute;
        bottom: -50px;
        color: rgba(0, 156, 127, 1);
        left: 50%;
        transform: translateX(-50%);
        background: rgba(229, 249, 246, 1);
      }

      .medal-crown {
        position: absolute;
        top: -12px;
        left: 50%;
        transform: translateX(-50%);
        width: 16px;
        height: 16px;
      }
    }
  }
}

// 按钮样式
.action-btn {
  display: flex;
  align-items: center;
  height: 19px;
  font-size: 14px;
  align-items: center;
  justify-content: space-between;
  color: #999999;
  cursor: pointer;
  .action-icon {
    width: 16px;
    height: 16px;
    margin-right: 6px;
    &.study-icon {
      background: url('@/assets/img/percision/pen.png') no-repeat center;
      background-size: contain;
      margin-right: 6px;
    }

    &.practice-icon {
      background: url('@/assets/img/percision/strong.png') no-repeat center;
      background-size: contain;
    }
  }

  &.study {
    background: #f8f9fa;
    padding-left: 6px;
    // border: 1px solid #e9ecef;
    text-decoration: underline;
    &:hover {
      // background: #e9ecef;
      color: #495057;
    }
  }

  &.practice {
    background: #f8f9fa;
    padding-left: 6px;
    // border: 1px solid #e9ecef;
    text-decoration: underline;
    &:hover {
      // background: #e9ecef;
      color: #495057;
    }
  }
}

// 成就徽章样式
.achievement-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 500;
  color: white;
  z-index: 2;

  &.challenge {
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  }

  &.retry {
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  }

  &.perfect {
    background: transparent;
    padding: 0;

    .perfect-icon {
      width: 60px;
      height: auto;
      filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
    }
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.lesson-section {
  animation: fadeInUp 0.5s ease-out;

  &:nth-child(1) { animation-delay: 0.1s; }
  &:nth-child(2) { animation-delay: 0.2s; }
  &:nth-child(3) { animation-delay: 0.3s; }
}

.knowledge-status {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: 500;
  color: white;
}

.status-mastered {
  background: linear-gradient(166.7deg, #08d8b8 0%, #00b392 100%);
}

.status-learning {
  background: linear-gradient(150.8deg, #5a85ec 0%, #3a65cc 100%);
}

.status-failed {
  background: linear-gradient(150.8deg, #f07f4c 0%, #c95656 100%);
}

.status-testing {
  background: linear-gradient(150.8deg, #f6d22b 0%, #f29500 100%);
}

.status-untested {
  background: #bbbbbb;
}

.status-unknown {
  background: #999999;
}

.knowledge-detail-content {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
  line-height: 1.6;
  font-size: 14px;
  color: #333;
  
  :deep(table) {
    border-collapse: collapse;
    width: 100%;
    margin: 10px 0;
    
    td, th {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: center;
    }
    
    tr:nth-child(even) {
      background-color: #f2f2f2;
    }
  }
  
  :deep(br) {
    margin-bottom: 5px;
  }
}

.knowledge-name {
  cursor: pointer;
  
  &:hover .knowledge-title {
    color: #5a85ec;
    text-decoration: underline;
  }
}

.lesson-container {
  border: 1px solid #EAEAEA;
  overflow: hidden;
  background: #FFFFFF;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  position: relative;
}

.lesson-title {
  text-align: center;
  font-size: 12px;
  color: #5A85EC;
  width: 60px;
  line-height: 24px;
  border: 1px solid #5A85EC;
  background: rgba(238, 243, 253, 1);
}

.lesson-content {
  padding: 10px 0 0 0;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  background: #F8F9FA;
  border-radius: 8px;
  margin: 20px 0;
}

.empty-message {
  font-size: 16px;
  color: #999;
}

.challenge-btn {
  width: 112px;
  height: 48px;
  background: url('@/assets/img/percision/training/challenge_btn_bg.png')center no-repeat;
  background-size: 100%;
  font-size: 16px;
  font-weight: 700;
  color: #fff;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 58%;
  transform: translateY(-50%);
  right: 20px;
  bottom: 20px;
  opacity: 1;
  transition: opacity 0.3s ease;
  
}
</style>
<style lang="scss" scoped>
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.elevate-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.elevate-ct {
  width: 812px;
  height: 516px;
  border-radius: 20px;
  position: relative;
  background:  url("@/assets/img/percision/training/ytzk.png") no-repeat;
  background-size: 100%;
  color: #fff;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}
.top-title{
  width: 160px;
  margin: 0 auto;
  padding-top: 86px;
  font-size: 16px;
  text-align: center;
  font-weight: bold;
  color: #fff; /* 内部为白色文字 */
  text-shadow: 
    1px 1px 0 rgba(217, 78, 50, 1),  
    -1px -1px 0 rgba(217, 78, 50, 1),  
    1px -1px 0 rgba(217, 78, 50, 1),  
    -1px 1px 0 rgba(217, 78, 50, 1); /* 外部为红色描边 */
  }
  .block-ct{
    width: 652px;
    height: 146px;background: rgba(255, 255, 255, 1);margin: 0 auto;margin-top: 20px;box-shadow: 0 0 3px 3px rgba(208, 192, 169, 1);border-radius: 14px;
    padding: 30px 50px 20px 30px;
    text-align: center;
    .book-list{
    padding: 0 50px 0 30px;
    display: flex;
    margin-bottom: 20px;
    img{
      width: 24px;
      height: 24px;
      margin-right: 8px;
    }
    .book-name{
      color: rgba(50, 58, 87, 1);
      font-size: 16px;
      width: 300px;
      text-align: left;
      white-space: nowrap;       /* 防止文字换行 */
      overflow: hidden;          /* 隐藏超出部分 */
      text-overflow: ellipsis; 
    }
    .book-tl{
      color: rgba(50, 58, 87, 1);
      font-size: 16px;
      padding-left: 46px;
      span{
        font-size: 20px;
        font-weight: 700;
        color: rgba(255, 151, 31, 1);
        padding-left: 10px;
      }
    }
  }
  .prompt{
    text-align: center;
    display: flex;
    color: rgba(42, 43, 42, 1);
    font-size: 16px;
    width: 260px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    display: inline-block;
    padding-top: 20px;
    span{
      font-size: 20px;
      color: rgba(255, 151, 31, 1);
    }
  }
  }
  .challenge-fq{
  display: flex;
  color: rgba(50, 58, 87, 1);
  justify-content: center;
  align-items: center;
  margin: 26px auto 0 auto;
  font-size: 16px;
  img{
    width: 66px;
    height: 30px;
    margin: 0 5px;
  }
}
.book-challenge{
    width: 112px;
    height: 46px;
    background: url('@/assets/img/percision/training/challenge_btn_bg.png')center no-repeat;
    background-size: 100%;
    font-weight: 700;
    color: #fff;
    cursor: pointer;
    font-size: 16px;
    line-height: 46px;
    text-align: center;
    margin: 50px auto 0 auto;
}
.click-bt{
 display: flex;
 cursor: pointer;
}

.close-btn {
  position: absolute;
  top: -10px;
  right: -20px;
  margin-top: 20px;
  border-radius: 50%;
  // background-color: rgba(0, 0, 0, 0.1);
  color: #333;
  font-size: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  img{
    width: 54px;
    height: 54px;
  }
}

.close-btn:hover {
  // background-color: rgba(0, 0, 0, 0.2);
  transform: scale(1.1);
}
</style>
