<template>
    <div style="display: flex;flex-direction: column;width: 100%;">
        <div class="top-box">
            <div class="top-content">
                <div class="goback" @click="goback"> <  返回</div>
                <div class="subject">
                    <div class="subject-title">
                        <img src="@/assets/img/academic/subject-title.png" />
                        <span>选择学科</span>
                    </div>
                    <div class="subject-content">
                        <div class="subject-item" v-for="(item, index) in subjectList" :class="item.selected?'selected':''" :key=index @click="setSubject(item)">
                            {{ item.subject }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="main-box">
            <div class="main-content">
                <div class="left">
                    <!-- {{ menuList }} -->
                    <div class="subject-item" v-for="(item, index) in menuList" :class="item.selected?'selected':''" :key=index @click="setMenu(item)">
                        <img :src="getUrl(item)" alt="" />
                        <span class="subject-item-text">{{item.name}}</span>
                    </div>
                    <div v-show="!menuList[0]?.selected &&!menuList[3]?.selected && showChapter" class="chapter-box">
                        <knowledgeTree
                            :selected="currentChapterId"
                            :isStatus="false"
                            :iswen="iswen"
                            :sty="'width:16.5rem;padding-top: .625rem!important'"
                            :options="apiType === 'improvement' ? improvementState.options : chapterState.options"
                            @setChapterId="setChapterId"
                            @setChapterName="setChapterName" />
                    </div>
                    <div v-show="!menuList[0]?.selected &&!menuList[3]?.selected && showChapter" @click="showChapter=!showChapter" class="chapter-btn open">
                        <span>《</span>
                    </div>
                    <div v-show="!menuList[0]?.selected &&!menuList[3]?.selected && !showChapter" @click="showChapter=!showChapter" class="chapter-btn">
                        <div>展</div>
                        <div>开</div>
                    </div>
                </div>
                <div class="right">
                    <div class="base-info">
                        <div class="base-info-title">AI精准学学情报告</div>
                        <div class="base-info-content">
                            <div class="base-info-content-left">
                                <div class="base-info-content-left-t">
                                    <div class="base-info-content-left-t-it">
                                        <div style="display: flex;align-items: center;"><span class="dot"></span> <span>姓名</span></div>
                                        <div class="big-black">{{ curLearnNow.nickName }}</div>
                                    </div>
                                    <div class="base-info-content-left-t-it">
                                        <div style="display: flex;align-items: center;"><span class="dot"></span> <span>账号</span></div>
                                        <div class="big-black">{{ getUserInfo }}</div>
                                    </div>
                                    <div class="base-info-content-left-t-it">
                                        <div style="display: flex;align-items: center;"><span class="dot"></span> <span>年级</span></div>
                                        <div class="big-black">{{ curLearnNow.gradeName }}</div>
                                    </div>
                                    <div class="base-info-content-left-t-it">
                                        <div style="display: flex;align-items: center;"><span class="dot"></span> <span>学制</span></div>
                                        <div class="big-black">{{ curLearnNow.academic == '1'?'六三制':'五四制' }}</div>
                                    </div>
                                </div>
                                <div class="base-info-content-left-t mgtop50">
                                    <div class="base-info-content-left-t-it">
                                        <div style="display: flex;align-items: center;"><span class="dot"></span> <span>学习教材</span></div>
                                        <div class="big-black">{{ bookVersionName }}</div>
                                    </div>
                                </div>
                            </div>
                            <div class="base-info-content-right">
                                <div class="qrcode-box">
                                    <img :src="qrSrc" :class="qrSrc?'':'opac'" />
                                </div>
                                <p>扫码查看<span>{{ learnNow.nickName }}同学</span>的学情报告</p>
                            </div>
                        </div>
                    </div>
                    <keep-alive v-if="comType">
                        <component
                            :is="comType"
                            :chapterId="currentChapterId"
                            :apiType="apiType"
                            :chapterName="currentChapterName"
                            :ref="setComponentRef" />
                    </keep-alive>
                    <!-- <Term v-if="menuList[0]?.selected"></Term>
                    <Chapter v-if="menuList[1]?.selected" :chapterId="chapterState.chapterId" :chapterName="chapterState.chapterName"></Chapter> -->
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router'
import { useRouteStoreHook } from '@/store/modules/route'
import { useUserStore } from "@/store/modules/user"
import { computed, markRaw, nextTick, onMounted, reactive, ref } from 'vue'
import { createAppletCodeApi } from "@/api/user"
import { getBookChapterListApi, getMasteryApi, } from "@/api/book"
import { getImprovementPointList, pointListList } from "@/api/video"

import { userGetAllApi } from "@/api/user"

import knowledgeTree from "@/views/components/knowledgeTree/index.vue"
import { storeToRefs } from 'pinia'
import Term from './components/term.vue'
import Chapter from './components/chapter.vue'
import Improve from './components/chapter.vue'
import Preparation from './components/term.vue'
const userStore = useUserStore()
const { learnNow, userInfo, subjectObj, chapterObj, improvementObj } = storeToRefs(userStore)
const subject = computed(() => {
  return subjectObj.value.subject
})
let chapterId2 = chapterObj.value.chapterId
chapterObj.value.chapterId = chapterId2?.replace('章节', '')?.replace('单元测试', '')
const router = useRouter()
// 定义动态组件
const componentsTypes: any = {
    Term,
    Chapter,
    Improve,
    Preparation
}
const comType = ref("")
const iswen = ref(true)//左侧菜单label
const apiType = ref("")//报告详情类型

// 章节报告模式的状态
const chapterState = reactive({
    chapterId:'',
    chapterName:'',
    options: []
})

// 提高模式的状态
const improvementState = reactive({
    chapterId:'',
    chapterName:'',
    options: []
})
const newNickName = ref('')
const qrSrc = ref('')
const loading = ref(false)
const showChapter = ref(false)
let isTest = false
if (chapterObj.value.chapterName) {
  isTest = chapterObj.value.chapterName.includes('单元测试')
}

const menuList = computed(() => {
  const allModes = [
    { id: 1, name: "学期报告", icon: "menu1", com: "Term",type:'', selected: true },
    { id: 2, name: "章节报告", icon: "menu2", com: "Chapter",type:'', selected: false },
    { id: 3, name: "提高模式", icon: "menu3", com: "Improve",type:'improvement',selected: false },
    { id: 4, name: "备考模式", icon: "menu4", com: "Preparation",type:'', selected: false }
  ];

  const highSchoolGrades = ['高一', '高二', '高三'];
  // 只有当学科是数学且年级是小学或初中时才显示提高模式
  if (subjectObj.value.subjectName === '数学' && !highSchoolGrades.includes(learnNow.value.gradeName)) {
    return allModes; // 保留所有模式，包括提高模式
  } else {
    // 其他情况过滤掉提高模式
    return allModes.filter(mode => mode.type !== 'improvement');
  }
})
const componentRef = ref()
const subjectList:any = ref(userStore.subjectList)

// 计算属性，确保传递给子组件的参数始终有效
const currentChapterId = computed(() => {
    if (apiType.value === 'improvement') {
        return improvementState.chapterId || ''
    } else {
        return chapterState.chapterId || ''
    }
})

const currentChapterName = computed(() => {
    if (apiType.value === 'improvement') {
        return improvementState.chapterName || ''
    } else {
        return chapterState.chapterName || ''
    }
})

const curLearnNow = computed(() => {
  return learnNow.value
})

const getUserApi = () => {
    userGetAllApi().then((res : any) => {
        newNickName.value = res.data.nickName
    })
}
onMounted(async()=> {
    // getUserApi()
    //匹配学生学科
    let versions=userStore.learnNow.versions,list:any=[]
    if(versions?.length){
      for(let i of versions){
        for(let n of subjectList.value){
          if(i.subject == n.desc){
            list.push(n)
          }
        }
      }
    }
    subjectList.value=list
    loading.value = true
    getQrcode()
    await getBookChapterList()
    loading.value = false
    // 初次默认渲染
    comType.value = markRaw(componentsTypes[comType.value || "Term"])
    if(componentRef.value) {
        nextTick(() => {
            safeInitComponent()
        })
    }
})
const setComponentRef = (el: any) => {
  componentRef.value = el
}

// 安全的组件初始化方法
const safeInitComponent = () => {
    if (componentRef.value && currentChapterId.value) {
        componentRef.value.init()
    }
}

//获取章节列表
const getBookChapterList =async (type?:string) => {
    let res: any = {}
    apiType.value=type||''
        if (type === 'improvement') {
            iswen.value=false
            // 如果type为improvement，调用getImprovementPointList
            res =await  getImprovementPointList({
                subject: subjectObj.value.id,
                hierarchy:2
            })
            if (res?.code == 200) {
                improvementState.options = res.data || []
                // 优先使用已保存的提高模式状态，否则使用默认值
                const defaultChapterId = isTest?(getLast(res.data[0]).id || ""):chapterObj.value.chapterId
                improvementState.chapterId = improvementObj.value.chapterId || (res.data[0]?.id || '') || defaultChapterId 
                improvementState.chapterName = improvementObj.value.chapterName || res.data[0]?.name || ''
                // 只有当chapterId存在时才初始化组件
                if (improvementState.chapterId) {
                    nextTick(() => {
                        safeInitComponent()
                    })
                }
            }
        } else {
            res =await getBookChapterListApi({
            bookId: subjectObj.value.bookId,
            hierarchy: 3,
            type: 0
            })
            if (res?.code == 200) {
                chapterState.options = res.data || []
                const defaultChapterId = isTest?(getLast(res.data[0]).id || ""):chapterObj.value.chapterId

                // 使用章节模式的状态
                chapterState.chapterId = chapterObj.value.chapterId ||  (res.data[0]?.id || '') || defaultChapterId 
                chapterState.chapterName = chapterObj.value.chapterName || res.data[0]?.name || ''

                // 只有当chapterId存在时才初始化组件
                if (chapterState.chapterId) {
                    nextTick(() => {
                        safeInitComponent()
                    })
                }
            }
        }
}
const getLast = (data: any) => {
  let item:any = null
  if(data?.children?.length > 0) {
    return getLast(data?.children?.[0])
  } else {
    item = data
    return item
  }
}
const setChapterName = (name: string) => {
  if (apiType.value === 'improvement') {
    improvementState.chapterName = isTest ? (name + getLast(improvementState.options[0]).name) : chapterObj.value.chapterName
  } else {
    chapterState.chapterName = isTest ? (name + getLast(chapterState.options[0]).name) : chapterObj.value.chapterName
  }
}
const setChapterId = (data: any, name: string) => {
    if (apiType.value === 'improvement') {
        improvementState.chapterId = data.id
        improvementState.chapterName = name + data.name
        nextTick(() => {
            userStore.setImprovementId(improvementState.chapterId, improvementState.chapterName)
            safeInitComponent()
        })
    } else {
        chapterState.chapterId = data.id
        chapterState.chapterName = name + data.name
        nextTick(() => {
            userStore.setChapterId(chapterState.chapterId, chapterState.chapterName)
            safeInitComponent()
        })
    }
}
const getUrl = (item: any) => {
    let url = item.icon
    if (item.selected) {
        url = item.icon + '_selected'
    }
    return new URL(`../../../assets/img/academic/${url}.png`, import.meta.url).href //静态资源引入为url，相当于require()
}
const goback = () => {
    let name = ""
    // 返回时重置tabbar选中
    router.options.routes.map(item => {
        if (item.redirect == router.options.history.state.back) {
            name = item.name as string
        }
    })
    useRouteStoreHook().setSelectMenu(name)
    router.push({
        path: '/ai_percision/knowledge_graph',
    })
}
const setMenu = (data: any) => {
    menuList.value.forEach(item => {
        if(item.name == data.name) {
            item.selected = true
        } else {
            item.selected = false
        }
    })

    sessionStorage.setItem("tabName", data.com)
    getBookChapterList(data.type)
    showChapter.value=false


    comType.value = markRaw(componentsTypes[data.com])
}
const setSubject = async (data: any) => {
    if (data.id !== subjectObj.value.id) {
        subjectList.value.forEach((item:any) => {
            if(item.desc == data.desc) {
                item.selected = true
            } else {
                item.selected = false
            }
        })
        userStore.setSubjectObj(data.desc, data.id)
        if (data.subject != '数学' && apiType.value) { 
            apiType.value=''
        }
        await getBookChapterList(apiType.value)

        if (apiType.value === 'improvement') {
            if (improvementState.options.length > 0) {
                improvementState.chapterId = getLast(improvementState.options[0]).id
                improvementState.chapterName = getFullName(improvementState.options, getLast(improvementState.options[0]))
                nextTick(() => {
                    userStore.setImprovementId(improvementState.chapterId, improvementState.chapterName)
                    if (improvementState.chapterId) {
                        safeInitComponent()
                    }
                })
            }
        } else {
            if (chapterState.options.length > 0) {
                chapterState.chapterId = getLast(chapterState.options[0]).id
                chapterState.chapterName = getFullName(chapterState.options, getLast(chapterState.options[0]))
                nextTick(() => {
                    userStore.setChapterId(chapterState.chapterId, chapterState.chapterName)
                    if (chapterState.chapterId) {
                        safeInitComponent()
                    }
                })
            }
        }
    }
}
const getFullName = (options: any, data: any) => {
  let arr = []
  const getName = (node: any, target: any, currentArr: any) => {
    node.map((item)=> {
      if(item.id == target) {
        arr = currentArr.slice()
      } else if (item.children && item.children.length > 0) {
        getName(item.children, target, currentArr.concat(item))
      }
    })
  }
  getName(options, data.id, [])
  let fullName = ""
  arr.map((item: any) => {
    fullName += ((item.name) + "/")
  })
  return fullName + data.name
}
const bookVersionName = computed(() => {
  const gradeId = learnNow.value.gradeId, gradeName = learnNow.value.gradeName,bookInfo = subjectObj.value
  let bookName = ""
  if (gradeId > 9) {
    bookName = bookInfo.editionName + gradeName + (bookInfo.typeName?.replace(bookInfo.editionName, '') || '')
  } else {
    bookName = bookInfo.editionName +  gradeName + (bookInfo.termName || '全年制')
  }
  return bookName
})
const getUserInfo = computed(()=> {
    const phone = JSON.parse(userInfo.value).phone
    return phone.substr(0, 3) + "****" + phone.substr(7, 4)
})
const getQrcode = () => {
    //生成小程序
    let param = {
      scene: subjectObj.value.bookId + '_' + learnNow.value.idNumber,
      appletType: 2,
      type: 1
    }
    createAppletCodeApi(param).then((res : any) => {
      // blob图片
      qrSrc.value = window.URL.createObjectURL(res.data)
    })
}
</script>
<style lang="scss" scoped>
.top-box {
    width: 100%;
    height: 5.8125rem;
    background-image: url(@/assets/img/academic/top-bg.png);
    background-size: cover;
    background-repeat: no-repeat;
    display: flex;
    justify-content: center;
    .top-content {
        width: 81.25rem;
        height: 100%;
        padding: .6875rem 0;
        box-sizing: border-box;
        .goback {
            color: #2a2b2a;
            font-size: .875rem;
            font-weight: 400;
            cursor: pointer;
            margin-bottom: .375rem;
        }
        .subject {
            width: 81.25rem;
            height: 2.875rem;
            line-height: 2.875rem;
            border-radius: 1.4375rem;
            border: .0625rem solid #ffffff;
            background: #ffffffb3;
            box-sizing: border-box;
            padding: 0 1rem;
            .subject-title {
                display: inline-flex;
                align-items: center;
                margin-right: 3.1875rem;
                img {
                    width: 1rem;
                    height: 1rem;
                    margin-right: .375rem;
                }
                span {
                    color: #5a85ec;
                    font-size: .875rem;
                    font-weight: 700;
                }
            }
            .subject-content {
                display: inline-flex;
                .subject-item {
                    width: 5rem;
                    height: 2.125rem;
                    color: #2a2b2a;
                    cursor: pointer;
                    text-align: center;
                    padding-bottom: .375rem;
                    &:hover {
                        color: #5a85ec;
                    }
                }
                .selected {
                    color: #5a85ec;
                    border-bottom: .125rem solid #5a85ec;
                }
            }
        }
    }
}
.main-box {
    width: 100%;
    height: calc(100vh - var(--v3-navigationbar-height) - 6.0625rem);
    background-image: url(@/assets/img/academic/main-bg.png);
    background-size: 100% 38.125rem;
    background-repeat: no-repeat;
    display: flex;
    justify-content: center;
    background-color: #F5F5F5;
    .main-content {
        width: 81.25rem;
        height: 100%;
        box-sizing: border-box;
        position: relative;
        display: flex;
        .chapter-box {
            position: absolute;
            left: 8.875rem;
            top: 0;
            width: 17.75rem;
            height: calc(100vh - var(--v3-navigationbar-height) - 5.8125rem);
            overflow-y: hidden;
            background: #fafcfd;
            box-shadow: .375rem .625rem .625rem 0 #0000000d;
            z-index: 9;
        }
        .chapter-btn {
            position: absolute;
            left: 9.125rem;
            top: calc((100vh - var(--v3-navigationbar-height) - 6.0625rem) / 2 - 2.8125rem);
            z-index: 9;
            width: 2rem;
            height: 6.0625rem;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            color: #ffffff;
            border-radius: 1.1875rem;
            background: #00c9a3;
            box-shadow: 0 0 .625rem 0 #0000000d;
            cursor: pointer;
        }
        .open {
            left: 27rem;
            span {
                margin-right: .5rem;
            }
        }
    }
}
.left {
    width: 8.875rem;
    height: 100%;
    background: #ffffff;
    box-sizing: border-box;
    overflow-y: auto;
    padding-top: 1.25rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    .subject-item {
        width: 7.125rem;
        height: 3.375rem;
        line-height: 3.375rem;
        text-align: center;
        border-radius: .25rem;
        cursor: pointer;
        font-size: 1rem;
        color: #2a2b2a;
        display: flex;
        align-items: center;
        justify-content: center;
        &:hover {
            background: #f5f5f5;
        }
        &:not(:last-child) {
            margin-bottom: 1.4375rem;
        }
        &:first-child {
            margin-top: 1.25rem;
        }
        img {
            width: 1.375rem;
            height: 1.375rem;
            margin-right: .5rem;
        }
    }
    .selected {
        background: #f1f7f6;
        color: #5A85EC;
    }

}
.right {
    margin-left: .625rem;
    height: calc(100vh - var(--v3-navigationbar-height) - 5.8125rem);
    overflow-y: auto;
    box-sizing: border-box;
    width: 71.75rem;
    .base-info {
        height: 21.8125rem;
        background-color: #ffffff;
        padding-top: .625rem;
        box-sizing: border-box;
        margin-bottom: 1.25rem;
        &-title {
            width: 24rem;
            height: 2.5625rem;
            line-height: 2.5625rem;
            // margin: .625rem 0;
            border-left: .3125rem solid #5A85EC;
            background: linear-gradient(270deg, #ffffff 0%, #eef3fd 100%);
            color: #5a85ec;
            font-size: 1.125rem;
            font-weight: 700;
            padding-left: .9375rem;
            box-sizing: border-box;
        }
        &-content {
            width: 100%;
            height: 16.75rem;
            margin-top: .625rem;
            background-image: url(@/assets/img/academic/info-bg.png);
            background-size: contain;
            background-repeat: no-repeat;
            padding: 1.25rem 2.5rem;
            box-sizing: border-box;
            display: flex;
            justify-content: space-between;
            &-left {
                padding-top: .5625rem;
                &-t {
                    &-it {
                        margin-right: 4.375rem;
                        display: inline-block;
                        span {
                            color: #666666;
                            font-size: .875rem;
                            font-weight: 400;
                        }
                    }
                }
            }
            &-right {
                width: 12rem;
                margin-right: 1.25rem;
                .qrcode-box {
                    width: 12rem;
                    height: 12rem;
                    border-radius: 1.875rem;
                    background: #ffffff;
                    box-shadow: 0 0 5.625rem 0 #0000001a;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex-direction: column;
                    img {
                        width: 10.25rem;
                        height: 10.25rem;
                    }
                }
                p {
                    color: #666666;
                    font-size: .75rem;
                    font-weight: 400;
                    text-align: center;
                    margin-top: 1.25rem;
                    span {
                        color: #5A85EC;
                    }
                }
            }
        }
    }
}
.dot {
    display: inline-block;
    width: .4375rem;
    height: .4375rem;
    border-radius: 50%;
    background: #666666;
    margin-right: .375rem;
}
.big-black {
    color: #2a2b2a;
    font-size: 1.25rem;
    font-weight: 700;
    margin-top: .625rem;
}
.mgtop50 {
    margin-top: 3.125rem;
}
</style>
